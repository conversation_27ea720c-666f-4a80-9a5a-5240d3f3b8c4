{"name": "aiso-backend", "version": "1.10.0-rc2", "license": "MIT", "author": "ItzCrazyKns", "scripts": {"start": "node dist/app.js", "start:migrate": "npm run db:push && node dist/app.js", "build": "tsc", "dev": "nodemon --ignore uploads/ src/app.ts ", "db:push": "drizzle-kit push:sqlite", "format": "prettier . --check", "format:write": "prettier . --write"}, "devDependencies": {"@cspotcode/source-map-support": "^0.8.1", "@tsconfig/node16": "^16.1.3", "@types/better-sqlite3": "^7.6.10", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/html-to-text": "^9.0.4", "@types/js-yaml": "^4.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.13.1", "@types/pdf-parse": "^1.1.4", "@types/readable-stream": "^4.0.11", "@types/ws": "^8.5.12", "drizzle-kit": "^0.22.7", "nodemon": "^3.1.0", "prettier": "^3.2.5", "ts-node": "^10.9.2", "typescript": "^5.4.3"}, "dependencies": {"@iarna/toml": "^2.2.5", "@langchain/anthropic": "^0.2.3", "@langchain/community": "^0.3.32", "@langchain/core": "^0.3.40", "@langchain/deepseek": "^0.0.1", "@langchain/google-genai": "^0.0.23", "@langchain/ollama": "^0.1.5", "@langchain/openai": "^0.4.4", "@mendable/firecrawl-js": "^1.18.6", "@types/cheerio": "^0.22.35", "@types/react-syntax-highlighter": "^15.5.13", "@xenova/transformers": "^2.17.1", "axios": "^1.6.8", "better-sqlite3": "^11.0.0", "cheerio": "^1.0.0", "compute-cosine-similarity": "^1.1.0", "compute-dot": "^1.1.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "drizzle-orm": "^0.31.2", "epub2": "^3.0.1", "express": "^4.19.2", "html-docx-js": "^0.3.1", "html-to-text": "^9.0.5", "js-yaml": "^4.1.0", "langchain": "^0.1.30", "lru-cache": "^11.0.2", "mammoth": "^1.8.0", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "playwright": "^1.50.1", "react-syntax-highlighter": "^15.6.1", "winston": "^3.13.0", "ws": "^8.17.1", "zod": "^3.22.4"}}