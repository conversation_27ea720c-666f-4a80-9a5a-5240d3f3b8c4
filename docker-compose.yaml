version: "3.9"

services:
  aiso-backend:
    build:
      context: .
      dockerfile: backend.dockerfile
    container_name: aiso-backend
    ports:
      - 13001:3001
    volumes:
      - ./volumes/backend_dbstore:/app/data
      - ./volumes/backend_uploads:/app/uploads
      - ./config.yaml:/app/config.yaml
      - ./topics.yaml:/app/topics.yaml
    networks:
      - aiso-network
    restart: unless-stopped

  aiso-frontend:
    build:
      context: .
      dockerfile: app.dockerfile
      args:
        - NEXT_PUBLIC_SITE_TITLE=AI助手
        - NEXT_PUBLIC_WS_URL=wss://aiso-api.01sworld.top:8443
        - NEXT_PUBLIC_API_URL=https://aiso-api.01sworld.top:8443/api
        - AUTHELIA_WELL_KNOWN_URL=https://sso.01sworld.top:8443/.well-known/openid-configuration
        - AUTHELIA_CLIENT_ID=aiso
        - AUTHELIA_CLIENT_SECRET=5c5hoCyS~OSrgrFQ.NeEvnjNmhUko5L8aFt-rMHqjSJ6gVFVTTDeQXCgdD53iwlvUCVeO~zK
        - NEXTAUTH_URL=https://aiso.01sworld.top:8443
        - NEXTAUTH_SECRET=KNG3NIETPDt+jXGUGjBBUmQtfMGPQxWGhz9Vqrgx2zY=
    container_name: aiso-frontend
    depends_on:
      - aiso-backend
    ports:
      - 13000:3000
    networks:
      - aiso-network
    restart: unless-stopped

networks:
  aiso-network:
