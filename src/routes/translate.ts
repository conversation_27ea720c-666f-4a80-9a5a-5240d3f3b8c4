import express from 'express';
import { translate } from '../utils/translate';
import logger from '../utils/logger';
import { getTranslateApiKey } from '../config';

const router = express.Router();

/* 兼容 LibreTranslate API 格式：
curl -X POST http://localhost:3001/api/translate \
  -H "Content-Type: application/json" \
  -d '{
    "q": "<PERSON> once told an employee his favorite president was <PERSON> because he was a ruthless populist who got stuff done, per new book",
    "source": "auto",
    "target": "zh",
    "format": "text",
    "alternatives": 3,
    "api_key": "1YNZaKOG8kWHRzftoWjzwLirmQAAfDXldQcLMyvb7Jg="
  }'
  */
 
interface TranslateBody {
  q: string;
  source?: string;
  target?: string;
  format?: string;
  alternatives?: number;
  api_key?: string;
}

interface TranslateResponse {
  translatedText: string;
  alternatives?: string[];
  detectedLanguage?: {
    confidence: number;
    language: string;
  };
}

router.post('/', async (req, res) => {
  try {
    const body: TranslateBody = req.body;
    
    // 验证必需参数
    if (!body.q) {
      return res.status(400).json({ message: 'Missing required parameter: q' });
    }

    // 验证 API 密钥
    const configApiKey = getTranslateApiKey();
    if (!configApiKey || body.api_key !== configApiKey) {
      return res.status(401).json({ message: 'Invalid API key' });
    }

    const translatedText = await translate(body.q);
    
    const response: TranslateResponse = {
      translatedText,
      alternatives: [],
      detectedLanguage: {
        confidence: 7,
        language: 'auto'
      }
    };

    res.status(200).json(response);
  } catch (err) {
    logger.error(`Translation error: ${err.message}`);
    res.status(500).json({ message: 'An error occurred during translation.' });
  }
});

export default router;