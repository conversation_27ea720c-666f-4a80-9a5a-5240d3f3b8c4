import express from 'express';
import {
  getChatModel,
  getEmbeddingModel,
} from '../lib/providers';
import {
  getOllamaApiEndpoint,
  getGeminiApiKey,
  getOpenaiApiKey,
  getCustomChatModel,
  getCustomEmbeddingModel,
  updateConfig,
} from '../config';
import logger from '../utils/logger';

const router = express.Router();

router.get('/', async (_, res) => {
  try {
    const config = {};

    // 获取模型信息
    try {
      await getChatModel();
      await getEmbeddingModel();
      
      // 添加模型配置信息
      config['chatModel'] = getCustomChatModel();
      config['embeddingModel'] = getCustomEmbeddingModel();
    } catch (err) {
      logger.error(`Error loading models: ${err.message}`);
    }

    config['openaiApiKey'] = getOpenaiApiKey();
    config['ollamaApiUrl'] = getOllamaApiEndpoint();
    config['geminiApiKey'] = getGeminiApiKey();

    res.status(200).json(config);
  } catch (err: any) {
    res.status(500).json({ message: 'An error has occurred.' });
    logger.error(`Error getting config: ${err.message}`);
  }
});

router.post('/', async (req, res) => {
  const config = req.body;

  const updatedConfig = {
    API_KEYS: {
      OPENAI: config.openaiApiKey,
      GEMINI: config.geminiApiKey,
    },
    API_ENDPOINTS: {
      OLLAMA: config.ollamaApiUrl,
    },
  };

  updateConfig(updatedConfig);

  res.status(200).json({ message: 'Config updated' });
});

export default router;
