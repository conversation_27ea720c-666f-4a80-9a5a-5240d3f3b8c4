import express from 'express';
import logger from '../utils/logger';
import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import type { Embeddings } from '@langchain/core/embeddings';
import {
  getChatModel,
  getEmbeddingModel,
  getSummarizerModel,
} from '../lib/providers';
import { searchHandlers } from '../websocket/messageHandler';
import { AIMessage, BaseMessage, HumanMessage } from '@langchain/core/messages';
import { MetaSearchAgentType } from '../search/metaSearchAgent';
import { fetchContentWithLink } from '../utils/documents';
import { discovers, linkContents } from '../db/schema';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import db from '../db';
import { sql } from 'drizzle-orm';

const router = express.Router();

interface chatModel {
  provider: string;
  model: string;
  customOpenAIBaseURL?: string;
  customOpenAIKey?: string;
}

interface embeddingModel {
  provider: string;
  model: string;
}

interface ChatRequestBody {
  optimizationMode: 'speed' | 'balanced';
  focusMode: string;
  chatModel?: chatModel;
  embeddingModel?: embeddingModel;
  query: string;
  history: Array<[string, string]>;
}

router.post('/', async (req, res) => {
  try {
    const body: ChatRequestBody = req.body;

    if (!body.focusMode || !body.query) {
      return res.status(400).json({ message: 'Missing focus mode or query' });
    }

    body.history = body.history || [];
    body.optimizationMode = body.optimizationMode || 'balanced';

    const history: BaseMessage[] = body.history.map((msg) => {
      if (msg[0] === 'human') {
        return new HumanMessage({
          content: msg[1],
        });
      } else {
        return new AIMessage({
          content: msg[1],
        });
      }
    });

    // 获取模型实例
    let llm: BaseChatModel | undefined;
    let summarizerLLM: BaseChatModel | undefined;
    let embeddings: Embeddings | undefined;

    try {
      // 直接获取模型实例
      llm = await getChatModel() as unknown as BaseChatModel;
      embeddings = await getEmbeddingModel() as unknown as Embeddings;
      summarizerLLM = await getSummarizerModel() as unknown as BaseChatModel;
    } catch (err) {
      logger.error(`Error loading models: ${err.message}`);
      return res.status(400).json({ message: 'Failed to load models' });
    }

    if (!llm || !embeddings || !summarizerLLM) {
      return res.status(400).json({ message: 'Invalid model selected' });
    }

    const searchHandler: MetaSearchAgentType = searchHandlers[body.focusMode];

    if (!searchHandler) {
      return res.status(400).json({ message: 'Invalid focus mode' });
    }

    const emitter = await searchHandler.searchAndAnswer(
      body.query,
      history,
      llm,
      summarizerLLM,
      embeddings,
      body.optimizationMode,
      [],
    );

    let message = '';
    let sources = [];

    emitter.on('data', (data) => {
      const parsedData = JSON.parse(data);
      if (parsedData.type === 'response') {
        message += parsedData.data;
      } else if (parsedData.type === 'sources') {
        sources = parsedData.data;
      }
    });

    emitter.on('end', () => {
      res.status(200).json({ message, sources });
    });

    emitter.on('error', (data) => {
      const parsedData = JSON.parse(data);
      res.status(500).json({ message: parsedData.data });
    });
  } catch (err: any) {
    logger.error(`Error in getting search results: ${err.message}`);
    res.status(500).json({ message: 'An error has occurred.' });
  }
});

router.post('/fetch', async (req, res) => {
  try {
    const { 
      url, 
      useAxios = true, 
      usePlaywright = true,
      useFirecrawl = false,
      useJina = false,
    } = req.body;
    
    if (!url) {
      return res.status(400).json({ message: '请提供要测试的 URL' });
    }

    const splitter = new RecursiveCharacterTextSplitter();
    const result = await fetchContentWithLink(url, splitter, { 
      useAxios, 
      usePlaywright,
      useFirecrawl,
      useJina,
    });
    return res.status(200).json({
      success: true,
      data: result
    });

  } catch (error) {
    logger.error(`测试抓取失败: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// 批量抓取发现内容
router.post('/fetch-auto', async (req, res) => {
  try {
    const { limit = 5, 
      useAxios = false, 
      usePlaywright = true,
      useFirecrawl = false,
      useJina = false,
    } = req.body;

    // 查找最新需要抓取的链接
    const needsFetch = await db.query.discovers
      .findMany({
        where: (discovers, { sql }) => {
          return sql`(
            NOT EXISTS (
              SELECT 1 FROM link_contents
              WHERE link_contents.link = discovers.url
            ) 
            OR EXISTS (
              SELECT 1 FROM link_contents
              WHERE link_contents.link = discovers.url
              AND (link_contents.contents IS NULL OR link_contents.contents = '[]')
            )
          ) AND (
            discovers.fetchStatus IS NULL
            OR discovers.fetchStatus = 'pending'
            OR (
              discovers.fetchStatus = 'failed' 
              AND discovers.fetchAttempts < 3
              AND (discovers.lastFetchAt IS NULL OR datetime(discovers.lastFetchAt) < datetime('now', '-1 day'))
            )
          )`;
        },
        orderBy: (discovers, { sql }) => [sql`createdAt DESC`],
        limit,
      });

    if (needsFetch.length === 0) {
      return res.status(200).json({
        success: true,
        data: { count: 0, urls: [], failedUrls: [] }
      });
    }

    const splitter = new RecursiveCharacterTextSplitter();
    const results = await Promise.allSettled(
      needsFetch.map(item => fetchContentWithLink(item.url, splitter, { 
        useAxios, 
        usePlaywright,
        useFirecrawl,
        useJina,
      }))
    );

    const { successUrls, failedUrls } = await results.reduce(
      async (accPromise, result, index) => {
        const acc = await accPromise;
        const url = needsFetch[index].url;
        const id = needsFetch[index].id;
        const success = result.status === 'fulfilled' && 
                        result.value.texts && 
                        result.value.texts.length > 0 && 
                        !result.value.texts[0].startsWith('Failed to retrieve content');
  
        // 更新数据库状态
        await db
          .update(discovers)
          .set({
            fetchStatus: success ? 'success' : 'failed',
            fetchAttempts: sql`${discovers.fetchAttempts} + 1`, 
            lastFetchAt: new Date().toISOString()
          })
          .where(sql`id = ${id}`);
  
        // 更新结果数组
        if (success) {
          acc.successUrls.push(url);
        } else {
          acc.failedUrls.push(url);
        }
        
        return acc;
      }, Promise.resolve({ successUrls: [] as string[], failedUrls: [] as string[] }));

    return res.status(200).json({
      success: true,
      data: {
        count: successUrls.length,
        urls: successUrls,
        failedUrls
      }
    });

  } catch (error) {
    logger.error(`批量抓取失败: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

export default router;
