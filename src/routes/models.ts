import express from 'express';
import logger from '../utils/logger';
import {
  getChatModel,
  getEmbeddingModel
} from '../lib/providers';
import { getCustomChatModel, getCustomEmbeddingModel } from '../config';

const router = express.Router();

router.get('/', async (req, res) => {
  try {
    // 获取当前配置的模型
    const [chatModel, embeddingModel] = await Promise.all([
      getChatModel(),
      getEmbeddingModel()
    ]);

    // 获取当前配置的模型名称
    const chatModelName = getCustomChatModel();
    const embeddingModelName = getCustomEmbeddingModel();

    // 构建响应对象
    const response = {
      chatModelProviders: {
        'jiaomu-ai-server': {
          [chatModelName]: {
            displayName: chatModelName
          }
        }
      },
      embeddingModelProviders: {
        'jiaomu-ai-server': {
          [embeddingModelName]: {
            displayName: embeddingModelName
          }
        }
      }
    };

    res.status(200).json(response);
  } catch (err) {
    res.status(500).json({ message: 'An error has occurred.' });
    logger.error(err.message);
  }
});

export default router;
