import { ChatOpenAI } from '@langchain/openai';
import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import type { Embeddings } from '@langchain/core/embeddings';
import {
  ChatPromptTemplate,
  MessagesPlaceholder,
  PromptTemplate,
} from '@langchain/core/prompts';
import {
  RunnableLambda,
  RunnableMap,
  RunnableSequence,
} from '@langchain/core/runnables';
import { BaseMessage } from '@langchain/core/messages';
import { StringOutputParser } from '@langchain/core/output_parsers';
import LineListOutputParser from '../lib/outputParsers/listLineOutputParser';
import LineOutputParser from '../lib/outputParsers/lineOutputParser';
import { getDocumentsFromLinks } from '../utils/documents';
import { Document } from 'langchain/document';
import { searchSearxng } from '../lib/searxng';
import path from 'path';
import fs from 'fs';
import { computeSimilarity } from '../utils/computeSimilarity';
import formatChatHistoryAsString from '../utils/formatHistory';
import eventEmitter from 'events';
import { StreamEvent } from '@langchain/core/tracers/log_stream';
import { IterableReadableStream } from '@langchain/core/utils/stream';
import { generateSummaryPrompt } from '../prompts/summarizer';
import { DocumentReranker } from './documentReranker';
import { KnowledgeSearcher } from './knowledgeSearcher';
import { optimizeSearchQuery } from '../utils/queryOptimizer';
import { KnowledgeChannel } from '../config/knowledge';

// MetaSearchAgent 接口定义，包含主要的搜索和回答功能
export interface MetaSearchAgentType {
  searchAndAnswer: (
    message: string,
    history: BaseMessage[],
    llm: BaseChatModel,
    summarizerLLM: BaseChatModel,  // 添加总结模型参数
    embeddings: Embeddings,
    optimizationMode: 'speed' | 'balanced' | 'quality',
    fileIds: string[],
  ) => Promise<eventEmitter>;
}

interface Config {
  searchWeb: boolean;
  searchKnowledge: boolean;
  knowledgeChannel: KnowledgeChannel;
  rerank: boolean;
  summarizer: boolean;
  rerankThreshold: number;
  queryGeneratorPrompt: string;
  responsePrompt: string;
  activeEngines: string[];
}

type BasicChainInput = {
  chat_history: BaseMessage[];
  query: string;
};

interface SearchResult {
  query: string;
  docs: Document[];
}

export class MetaSearchAgent implements MetaSearchAgentType {
  private config: Config;
  private userId: string;
  private strParser = new StringOutputParser();

  private documentReranker: DocumentReranker;
  private knowledgeSearcher: KnowledgeSearcher;
  
  private currentChatHistory: BaseMessage[] = [];
  private static readonly MAX_DOCS_PER_URL = 30; // 每个 URL 允许的最大文档数量

  constructor(config: Config, userId: string = 'default-user') {
    this.config = config;
    this.userId = userId;
    this.documentReranker = new DocumentReranker(config.rerankThreshold);
    this.knowledgeSearcher = new KnowledgeSearcher();
  }

  private async createQueryRewriterChain(summarizerLLM: BaseChatModel) {
    // 设置总结模型的温度为0，确保输出的一致性
    (summarizerLLM as unknown as ChatOpenAI).temperature = 0;

    // 构建一个按顺序执行的处理链
    return RunnableSequence.from([
      // 1. 使用提供的模板生成查询
      PromptTemplate.fromTemplate(this.config.queryGeneratorPrompt),
      // 2. 使用 LLM 处理查询
      summarizerLLM,
      // 3. 将结果转换为字符串
      this.strParser,
      // 4. 处理搜索结果
      RunnableLambda.from(async (input: string) => {
        // 解析问题
        const { links, question } = await this.parseInputContent(input);

        // 如果是not_needed，返回空字符串
        if (question === 'not_needed') {
          return { links: [], question: '' };
        }

        // 返回重述后的问题
        return { links, question };
      }),
    ]);
  }

  // 解析输入内容，提取链接和问题
  private async parseInputContent(input: string) {
    const linksOutputParser = new LineListOutputParser({ key: 'links' });
    const questionOutputParser = new LineOutputParser({ key: 'question' });

    const [links, parsedQuestion] = await Promise.all([
      linksOutputParser.parse(input),
      this.config.summarizer ? questionOutputParser.parse(input) : input,
    ]);

    return { links, question: parsedQuestion };
  }

  // 处理链接模式
  private async handleLinksMode(
    links: string[],
    question: string,
  ): Promise<SearchResult> {
    // 如果问题为空，默认使用总结模式
    const finalQuestion = question.length === 0 ? 'summarize' : question;
    
    // 获取链接文档
    const linkDocs = await getDocumentsFromLinks({ links });
    
    // 对文档进行分组处理
    const docGroups = await this.groupDocumentsByUrl(linkDocs);
    
    // 处理文档内容
    const processedDocs = await this.processDocGroups(docGroups);
    
    return { 
      query: finalQuestion, 
      docs: processedDocs 
    };
  }

  // 按URL对文档进行分组
  private async groupDocumentsByUrl(docs: Document[]): Promise<Document[]> {
    const docGroups: Document[] = [];

    // 按 URL 分组处理文档，控制每个 URL 的文档数量
    docs.forEach((doc) => {
      const URLDocExists = docGroups.find(
        (d) =>
          d.metadata.url === doc.metadata.url &&
          d.metadata.totalDocs < MetaSearchAgent.MAX_DOCS_PER_URL,
      );

      // 如果是新的 URL，创建新的文档组
      if (!URLDocExists) {
        docGroups.push({
          ...doc,
          metadata: {
            ...doc.metadata,
            totalDocs: 1,
          },
        });
        return;
      }

      // 如果 URL 已存在且未达到限制，追加内容
      const docIndex = docGroups.findIndex(
        (d) =>
          d.metadata.url === doc.metadata.url &&
          d.metadata.totalDocs < MetaSearchAgent.MAX_DOCS_PER_URL,
      );

      if (docIndex !== -1) {
        docGroups[docIndex].pageContent += `\n\n${doc.pageContent}`;
        docGroups[docIndex].metadata.totalDocs += 1;
      }
    });

    return docGroups;
  }

  // 处理文档组
  private async processDocGroups(
    docGroups: Document[], 
  ): Promise<Document[]> {
    return Promise.all(
      docGroups.map(async (doc) => {
        return new Document({
          pageContent: doc.pageContent,
          metadata: {
            title: doc.metadata.title,
            url: doc.metadata.url,
          },
        });
      })
    );
  }

  // 处理搜索模式
  private async handleSearchMode(question: string, history: BaseMessage[] = []): Promise<SearchResult> {
    const res = await searchSearxng(question, {
      language: 'en',
      engines: this.config.activeEngines,
      pageno: 1
    });

    // 将搜索结果转换为文档对象
    const documents = res.results.map((result) => 
      new Document({
        pageContent: this.getSearchResultContent(result),
        metadata: {
          title: result.title,
          url: result.url,
          ...(result.img_src && { img_src: result.img_src }),
        },
      })
    );

    return { query: question, docs: documents };
  }

  // 获取搜索结果内容
  private getSearchResultContent(result: any): string {
    return result.content || 
      (this.config.activeEngines.includes('youtube') ? result.title : '');
  }

  private async createAnsweringChain(
    llm: BaseChatModel,
    summarizerLLM: BaseChatModel,
    fileIds: string[],
    embeddings: Embeddings,
    optimizationMode: 'speed' | 'balanced' | 'quality',
  ) {
    // 创建一个顺序执行的处理链
    return RunnableSequence.from([
      // 1. 创建并行处理的映射，同时处理多个输入
      RunnableMap.from({
        // 直接传递用户查询
        query: (input: BasicChainInput) => input.query,
        // 传递对话历史
        chat_history: (input: BasicChainInput) => {
          // 保存当前聊天历史供后续使用
          this.currentChatHistory = input.chat_history;
          return input.chat_history;
        },
        // 添加当前时间戳
        date: () => new Date().toISOString(),
        // 核心上下文处理逻辑
        context: RunnableLambda.from(async (input: BasicChainInput) => {
          // 将对话历史格式化为字符串
          const processedHistory = formatChatHistoryAsString(
            input.chat_history,
          );

          let docs: Document[] | null = null;
          let query = input.query;
          console.log('原始 Query:', query);
          
          // 检查是否是直接的总结链接请求
          if (query.toLowerCase().startsWith('summary: http')) {
            const url = query.substring(9).trim();
            console.log('直接处理链接总结模式：', url);
            const linkResult = await this.handleLinksMode([url], 'summarize');
            docs = linkResult.docs;
            query = linkResult.query;
            
            // 直接返回处理后的文档，跳过后续搜索步骤
            const sortedDocs = await this.documentReranker.rerankDocs(
              query,
              docs,
              fileIds,
              embeddings,
              optimizationMode,
              []  // 链接模式下不使用知识库文档
            );
            
            return sortedDocs;
          }
          
          // 创建问题重写链
          const queryRewriterChain = await this.createQueryRewriterChain(summarizerLLM);

          // 使用问题重写链处理查询
          let rewrittenQuery = '';
          if (this.config.queryGeneratorPrompt !== '') {
            const { links, question } = await queryRewriterChain.invoke({
              chat_history: processedHistory,
              query,
            });
            
            // 处理链接模式
            if (links.length > 0) {
              console.log('处理链接模式：', links);
              const linkResult = await this.handleLinksMode(links, query);
              docs = linkResult.docs;
              query = linkResult.query;
              
              // 直接返回处理后的文档，跳过后续搜索步骤
              const sortedDocs = await this.documentReranker.rerankDocs(
                query,
                docs,
                fileIds,
                embeddings,
                optimizationMode,
                []  // 链接模式下不使用知识库文档
              );
              
              return sortedDocs;
            }

            if (question) {
              rewrittenQuery = question;
              console.log('优化后的 Query:', rewrittenQuery);
            }
          }

          // 处理网络搜索
          if (this.config.searchWeb && rewrittenQuery) {
            // 执行网络搜索
            const searchResult = await this.handleSearchMode(rewrittenQuery, input.chat_history);
            docs = searchResult.docs;
          }

          // 获取知识库搜索结果
          let knowledgeDocs = [];
          if (this.config.searchKnowledge && rewrittenQuery) {
            knowledgeDocs = await this.knowledgeSearcher.search(rewrittenQuery, this.userId, this.config.knowledgeChannel);
          }

          // 传入知识库文档进行重排序
          const sortedDocs = await this.documentReranker.rerankDocs(
            rewrittenQuery,
            docs ?? [],
            fileIds,
            embeddings,
            optimizationMode,
            knowledgeDocs
          );

          return sortedDocs;
        })
          // 设置检索器名称
          .withConfig({
            runName: 'FinalSourceRetriever',
          })
          // 处理文档格式化
          .pipe(this.processDocs),
      }),

      // 2. 创建聊天提示模板
      ChatPromptTemplate.fromMessages([
        ['system', this.config.responsePrompt],
        new MessagesPlaceholder('chat_history'),
        ['user', '{query}'],
      ]),

      // 3. 使用语言模型生成回答
      llm,

      // 4. 将输出解析为字符串
      this.strParser,
    ]).withConfig({
      runName: 'FinalResponseGenerator',
    });
  }

  private processDocs(docs: Document[]) {
    return docs
      .map(
        (_, index) =>
          `${index + 1}. ${docs[index].metadata.title} ${docs[index].pageContent}`,
      )
      .join('\n');
  }

  private async handleStream(
    stream: IterableReadableStream<StreamEvent>,
    emitter: eventEmitter,
  ) {
    for await (const event of stream) {
      // 处理源文档检索完成事件
      if (
        event.event === 'on_chain_end' &&
        event.name === 'FinalSourceRetriever'
      ) {
        emitter.emit(
          'data',
          JSON.stringify({ type: 'sources', data: event.data.output }),
        );
      }
      // 处理所有 LLM 的流式输出
      if (event.event === 'on_llm_stream') {
        const message = event.data.chunk.message;
        // 处理 ChatDeepSeek 的推理内容
        if (event.name === 'ChatDeepSeek' && message?.additional_kwargs?.reasoning_content) {
          emitter.emit(
            'data',
            JSON.stringify({
              type: 'reasoning',
              data: message.additional_kwargs.reasoning_content,
            }),
          );
        }
        // 处理所有模型的常规内容
        if (event.data.chunk.text) {
          emitter.emit(
            'data',
            JSON.stringify({ type: 'response', data: event.data.chunk.text }),
          );
        } else if (message?.content) {
          emitter.emit(
            'data',
            JSON.stringify({ type: 'response', data: message.content }),
          );
        }
      }
      // 处理最终响应生成完成事件
      if (
        event.event === 'on_chain_end' &&
        event.name === 'FinalResponseGenerator'
      ) {
        emitter.emit('end');
      }
    }
  }

  // 主要的搜索和回答方法，整合了所有功能链
  async searchAndAnswer(
    message: string,
    history: BaseMessage[],
    llm: BaseChatModel,
    summarizerLLM: BaseChatModel,  // 添加 summarizerLLM 参数
    embeddings: Embeddings,
    optimizationMode: 'speed' | 'balanced' | 'quality',
    fileIds: string[],
  ) {
    // 创建事件发射器用于处理流式输出
    const emitter = new eventEmitter();

    const answeringChain = await this.createAnsweringChain(
      llm,
      summarizerLLM,  // 传入 summarizerLLM
      fileIds,
      embeddings,
      optimizationMode,
    );

    const stream = answeringChain.streamEvents(
      {
        chat_history: history,
        query: message,
      },
      {
        version: 'v1',
      },
    );

    this.handleStream(stream, emitter);

    return emitter;
  }
}

export default MetaSearchAgent;
