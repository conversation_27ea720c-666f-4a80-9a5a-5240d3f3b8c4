import { Document } from 'langchain/document';
import { Embeddings } from '@langchain/core/embeddings';
import path from 'path';
import fs from 'fs';
import { computeSimilarity } from '../utils/computeSimilarity';

/**
 * 文档重排序器
 * 负责处理和重新排序搜索结果和本地文档
 */
export class DocumentReranker {
    private static readonly MAX_DOCS_PER_SEARCH = 30; // 每次搜索返回的最大文档数量
    private static readonly MAX_DOCS_WHEN_MIXED = 20; // 混合搜索结果时的本地文档数量
    private static readonly DEFAULT_RERANK_THRESHOLD = 0.3; // 文档重排序的相似度阈值

    constructor(private rerankThreshold?: number) { }

    /**
     * 重新排序文档
     * @param query 查询字符串
     * @param docs 网络搜索文档
     * @param fileIds 本地文件ID列表
     * @param embeddings 向量化工具
     * @param optimizationMode 优化模式
     * @param knowledgeDocs 知识库文档
     */
    async rerankDocs(
        query: string,
        docs: Document[],
        fileIds: string[],
        embeddings: Embeddings,
        optimizationMode: 'speed' | 'balanced' | 'quality',
        knowledgeDocs: Document[] = [],
    ): Promise<Document[]> {
        // 如果没有其他文档，直接返回知识库搜索结果
        if (docs.length === 0 && fileIds.length === 0) {
            return knowledgeDocs;
        }

        // 步骤1: 处理本地文件和搜索文档
        const filesData = await this.processLocalFiles(fileIds);
        const docsWithContent = docs.filter(doc => doc.pageContent?.length > 0);
        const fileDocs = this.convertFilesToDocuments(filesData);

        // 步骤2: 判断是否需要跳过向量化
        const shouldSkipVectorization = 
            optimizationMode === 'quality' || 
            query.toLowerCase() === 'summarize';

        // 步骤3: 根据不同情况选择处理策略
        if (shouldSkipVectorization) {
            // 质量模式或总结模式：直接合并文档
            return this.mergeDocs(docsWithContent, fileDocs, knowledgeDocs);
        }

        // 步骤4: 根据优化模式选择不同的处理方法
        let results: Document[];
        if (optimizationMode === 'speed') {
            results = await this.handleSpeedMode(query, docsWithContent, filesData, fileDocs, embeddings);
        } else if (optimizationMode === 'balanced') {
            results = await this.handleBalancedMode(query, docsWithContent, filesData, embeddings);
        } else {
            results = this.mergeDocs(docsWithContent, fileDocs);
        }

        // 合并知识库搜索结果
        return this.mergeDocs(results, [], knowledgeDocs);
    }

    /**
     * 处理本地文件
     * 读取文件内容和预计算的向量
     */
    private async processLocalFiles(fileIds: string[]) {
        return fileIds
            .map((file) => {
                const filePath = path.join(process.cwd(), 'uploads', file);
                const contentPath = filePath + '-extracted.json';
                const embeddingsPath = filePath + '-embeddings.json';

                try {
                    // 读取文件内容和向量
                    const content = JSON.parse(fs.readFileSync(contentPath, 'utf8'));
                    const embeddings = fs.existsSync(embeddingsPath) 
                        ? JSON.parse(fs.readFileSync(embeddingsPath, 'utf8')) 
                        : null;

                    // 返回处理后的文件数据
                    return content.contents.map((c: string, i: number) => ({
                        fileName: Buffer.from(content.title).toString('utf8'),
                        content: c,
                        embeddings: embeddings?.embeddings?.[i] || null,
                    }));
                } catch (error) {
                    console.error(`处理文件失败: ${file}`, error);
                    return [];
                }
            })
            .flat()
            .filter(item => item.content);
    }

    /**
     * 将文件数据转换为统一的 Document 格式
     */
    private convertFilesToDocuments(filesData: Array<{fileName: string, content: string}>) {
        return filesData.map(fileData => new Document({
            pageContent: fileData.content,
            metadata: {
                title: fileData.fileName,
                url: 'File',
            },
        }));
    }

    /**
     * 合并并限制文档数量
     */
    private mergeDocs(docs: Document[], fileDocs: Document[], additionalDocs: Document[] = []) {
        return [...docs, ...fileDocs, ...additionalDocs]
            .slice(0, DocumentReranker.MAX_DOCS_PER_SEARCH);
    }

    /* 速度优先模式：
    - 只处理本地文件的相似度计算，使用预计算的向量进行快速相似度匹配，返回相似度高于阈值的文档
    - 如果有网络搜索结果，则混合返回结果
    */
    private async handleSpeedMode(
        query: string,
        docsWithContent: Document[],
        filesData: Array<{fileName: string, content: string, embeddings: number[]}>,
        fileDocs: Document[],
        embeddings: Embeddings,
    ) {
        // 如果没有本地文件，直接返回搜索结果
        if (filesData.length === 0) {
            return docsWithContent.slice(0, DocumentReranker.MAX_DOCS_PER_SEARCH);
        }

        // 计算查询向量
        const queryEmbedding = await embeddings.embedQuery(query);
        
        // 计算相似度并排序
        const similarity = filesData.map((fileData, i) => ({
            index: i,
            similarity: computeSimilarity(queryEmbedding, fileData.embeddings),
        }));

        // 筛选相关文档
        let sortedDocs = similarity
            .filter(sim => sim.similarity > (this.rerankThreshold ?? DocumentReranker.DEFAULT_RERANK_THRESHOLD))
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, DocumentReranker.MAX_DOCS_PER_SEARCH)
            .map(sim => fileDocs[sim.index]);

        // 处理混合模式
        if (docsWithContent.length > 0) {
            sortedDocs = sortedDocs.slice(0, DocumentReranker.MAX_DOCS_WHEN_MIXED);
        }

        // 合并结果
        return [
            ...sortedDocs,
            ...docsWithContent.slice(0, DocumentReranker.MAX_DOCS_PER_SEARCH - sortedDocs.length),
        ];
    }

    /* 平衡模式
    - 同时处理网络搜索结果和本地文件，为所有文档计算向量表示，统一进行相似度计算和排序
    - 返回相似度最高的文档
    */
    private async handleBalancedMode(
        query: string,
        docsWithContent: Document[],
        filesData: Array<{fileName: string, content: string, embeddings: number[]}>,
        embeddings: Embeddings,
    ) {
        // 计算向量
        const [docEmbeddings, queryEmbedding] = await Promise.all([
            embeddings.embedDocuments(docsWithContent.map(doc => doc.pageContent)),
            embeddings.embedQuery(query),
        ]);

        // 合并文档和向量
        const allDocs = [...docsWithContent, ...this.convertFilesToDocuments(filesData)];
        const allEmbeddings = [...docEmbeddings, ...filesData.map(f => f.embeddings)];

        // 计算相似度并排序
        const similarity = allEmbeddings.map((docEmbedding, i) => ({
            index: i,
            similarity: computeSimilarity(queryEmbedding, docEmbedding),
        }));

        // 返回排序后的文档
        return similarity
            .filter(sim => sim.similarity > (this.rerankThreshold ?? DocumentReranker.DEFAULT_RERANK_THRESHOLD))
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, DocumentReranker.MAX_DOCS_PER_SEARCH)
            .map(sim => allDocs[sim.index]);
    }
}