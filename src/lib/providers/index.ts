import { loadSummarizerModel, loadTranslatorModel, loadChatModel, loadEmbeddingModel } from './openai';

// 添加缓存对象
let modelCache = {
  chat: null,
  embedding: null,
  summarizer: null,
  translator: null,
};

export const getChatModel = async () => {
  if (modelCache.chat) {
    return modelCache.chat;
  }

  try {
    const model = await loadChatModel();
    if (!model) {
      throw new Error('Failed to load chat model');
    }
    modelCache.chat = model;
    return model;
  } catch (err) {
    console.error('Error loading chat model:', err);
    throw err;
  }
};

export const getEmbeddingModel = async () => {
  if (modelCache.embedding) {
    return modelCache.embedding;
  }

  try {
    const model = await loadEmbeddingModel();
    if (!model) {
      throw new Error('Failed to load embedding model');
    }
    modelCache.embedding = model;
    return model;
  } catch (err) {
    console.error('Error loading embedding model:', err);
    throw err;
  }
};

export const getSummarizerModel = async () => {
  if (modelCache.summarizer) {
    return modelCache.summarizer;
  }

  try {
    const model = await loadSummarizerModel();
    if (!model) {
      throw new Error('Failed to load summarizer model');
    }
    modelCache.summarizer = model;
    return model;
  } catch (err) {
    console.error('Error loading summarizer model:', err);
    throw err;
  }
};

export const getTranslatorModel = async () => {
  if (modelCache.translator) {
    return modelCache.translator;
  }

  try {
    const model = await loadTranslatorModel();
    if (!model) {
      throw new Error('Failed to load translator model');
    }
    modelCache.translator = model;
    return model;
  } catch (err) {
    console.error('Error loading translator model:', err);
    throw err;
  }
};

export const clearModelCache = () => {
  modelCache = {
    chat: null,
    embedding: null,
    summarizer: null,
    translator: null,
  };
};
