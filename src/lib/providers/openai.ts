import { Chat<PERSON>penA<PERSON>, OpenAIEmbeddings } from '@langchain/openai';
import { ChatDeepSeek } from '@langchain/deepseek';
import { getOpenaiApiKey, getCustomBaseURL, getCustomSummarizerModel, getCustomTranslatorModel, getCustomChatModel, getCustomEmbeddingModel } from '../../config';
import logger from '../../utils/logger';

const inferenceModels = ['deepseek-r1', 'qwen3-235b-a22b'];

export const loadChatModel = async () => {
  const openAIApiKey = getOpenaiApiKey();
  const baseURL = getCustomBaseURL();
  const modelName = getCustomChatModel();

  if (!openAIApiKey || !modelName) return null;

  try {
    // 根据模型名称选择不同的模型实例
    if (inferenceModels.includes(modelName)) {
      return new ChatDeepSeek({
        apiKey: openAIApiKey,
        model: modelName,
        configuration: {
          baseURL,
        }
      });
    }
    
    return new ChatOpenAI({
      openAIApiKey,
      modelName,
      temperature: 0.7,
      configuration: {
        baseURL,
      }
    });
  } catch (err) {
    logger.error(`Error loading chat model: ${err}`);
    return null;
  }
};

export const loadEmbeddingModel = async () => {
  const openAIApiKey = getOpenaiApiKey();
  const baseURL = getCustomBaseURL();
  const modelName = getCustomEmbeddingModel();

  if (!openAIApiKey || !modelName) return null;

  try {
    return new OpenAIEmbeddings({
      openAIApiKey,
      modelName,
      configuration: {
        baseURL,
      }
    });
  } catch (err) {
    logger.error(`Error loading embedding model: ${err}`);
    return null;
  }
};

export const loadSummarizerModel = async () => {
  const openAIApiKey = getOpenaiApiKey();
  const baseURL = getCustomBaseURL();
  const modelName = getCustomSummarizerModel();

  if (!openAIApiKey || !modelName) return null;

  try {
    return new ChatOpenAI({
      openAIApiKey,
      modelName,
      temperature: 0,  // 总结任务使用较低的温度
      configuration: {
        baseURL,
      }
    });
  } catch (err) {
    logger.error(`Error loading summarizer model: ${err}`);
    return null;
  }
};

export const loadTranslatorModel = async () => {
  const openAIApiKey = getOpenaiApiKey();
  const baseURL = getCustomBaseURL();
  const modelName = getCustomTranslatorModel();

  if (!openAIApiKey || !modelName) return null;

  try {
    const model = new ChatOpenAI({
      openAIApiKey,
      modelName,
      temperature: 0,  // 翻译任务使用较低的温度
      configuration: {
        baseURL,
      }
    });

    return model;
  } catch (err) {
    logger.error(`Error loading translator model: ${err}`);
    return null;
  }
};
