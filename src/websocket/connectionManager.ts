import { WebSocket } from 'ws';
import { handleMessage } from './messageHandler';
import {
  getChatModel,
  getEmbeddingModel,
} from '../lib/providers';
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import type { Embeddings } from '@langchain/core/embeddings';
import type { IncomingMessage } from 'http';
import logger from '../utils/logger';

const HEARTBEAT_INTERVAL = 30000; // 30秒发送一次心跳

export const handleConnection = async (
  ws: WebSocket,
  request: IncomingMessage,
) => {
  try {
    let llm: BaseChatModel | undefined;
    let embeddings: Embeddings | undefined;

    // 使用配置的模型
    try {
      llm = await getChatModel() as unknown as BaseChatModel;
      embeddings = await getEmbeddingModel() as unknown as Embeddings;
    } catch (error) {
      logger.error('Error loading models:', error);
      ws.send(JSON.stringify({
        type: 'error',
        data: 'Failed to load models. Please check your configuration.',
        key: 'MODEL_LOADING_ERROR',
      }));
      ws.close();
      return;
    }

    if (!llm || !embeddings) {
      ws.send(JSON.stringify({
        type: 'error',
        data: 'Invalid LLM or embeddings model selected, please refresh the page and try again.',
        key: 'INVALID_MODEL_SELECTED',
      }));
      ws.close();
      return;  // 添加 return 防止继续执行
    }

    // 添加心跳检测
    let isAlive = true;
    const heartbeat = setInterval(() => {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify({ type: 'ping' }));
      } else {
        clearInterval(heartbeat);
      }
    }, HEARTBEAT_INTERVAL);

    const interval = setInterval(() => {
      if (ws.readyState === ws.OPEN) {
        ws.send(
          JSON.stringify({
            type: 'signal',
            data: 'open',
          }),
        );
        clearInterval(interval);
      }
    }, 5);

    ws.on('message', async (message) => {
      const data = JSON.parse(message.toString());
      if (data.type === 'pong') {
        isAlive = true;
        return;
      }
      await handleMessage(message.toString(), ws, llm, embeddings);
    });

    ws.on('close', () => logger.debug('Connection closed'));
  } catch (err) {
    ws.send(
      JSON.stringify({
        type: 'error',
        data: 'Internal server error.',
        key: 'INTERNAL_SERVER_ERROR',
      }),
    );
    ws.close();
    logger.error(err);
  }
};
