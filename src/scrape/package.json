{"name": "scraper-service", "version": "1.0.0", "private": true, "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "ts-node-dev --respawn src/app.ts", "clean": "rm -rf dist"}, "dependencies": {"@mendable/firecrawl-js": "^1.18.6", "@types/cheerio": "^0.22.35", "axios": "^1.6.7", "cheerio": "^1.0.0", "cors": "^2.8.5", "express": "^4.18.2", "html-to-text": "^9.0.5", "pdf-parse": "^1.1.1", "playwright": "^1.42.1", "winston": "^3.11.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/html-to-text": "^9.0.4", "@types/node": "^20.11.24", "@types/pdf-parse": "^1.1.4", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}, "main": "index.js", "author": "", "license": "ISC", "description": ""}