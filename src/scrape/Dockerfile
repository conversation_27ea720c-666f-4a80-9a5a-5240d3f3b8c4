FROM node:20.18.0-alpine

# 安装 Chromium 依赖
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    nodejs \
    yarn

# 设置 Chromium 和 Playwright 环境变量
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1 \
    PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH=/usr/bin/chromium \
    PORT=3002

WORKDIR /app

# 复制应用文件
COPY . /app/

# 安装依赖并构建
RUN yarn config set registry https://registry.npmmirror.com \
    && yarn install --frozen-lockfile \
    && yarn build

# CMD ["yarn", "start"]