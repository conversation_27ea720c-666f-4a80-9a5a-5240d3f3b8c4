#!/bin/bash

# 设置变量
SOURCE_DIR="$(pwd)"
TARGET_DIR="$HOME/Program/docker/01.ai.aiso/scraper-service"
LOGS_DIR="$TARGET_DIR/logs"

# 1. 编译项目
echo "开始编译项目..."
yarn install
yarn build

if [ $? -ne 0 ]; then
    echo "编译失败，退出发布流程"
    exit 1
fi

# 2. 检测和创建目录
echo "创建目标目录..."
mkdir -p "$TARGET_DIR"
mkdir -p "$LOGS_DIR"

# 3. 复制编译后的文件
echo "复制文件到目标目录..."
cp -r dist package.json yarn.lock "$TARGET_DIR/"

# 4. 创建并复制启动脚本
echo "创建启动脚本..."
cat > "$TARGET_DIR/start.sh" << 'EOL'
#!/bin/bash
export PORT=13002
export NODE_ENV=production

# 查找 Playwright Chromium 的具体路径
CHROMIUM_PATH=$(find ~/Library/Caches/ms-playwright -name "chromium-*" -type d | head -n 1)
if [ -n "$CHROMIUM_PATH" ]; then
    export PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH="$CHROMIUM_PATH/chrome-mac/Chromium.app/Contents/MacOS/Chromium"
    
    if [ ! -f "$PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH" ]; then
        echo "警告: Playwright Chromium 未找到，尝试使用系统 Chrome..."
        export PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    fi
else
    echo "警告: Playwright Chromium 路径未找到，使用系统 Chrome..."
    export PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
fi

# 检查 pm2 是否安装
if ! command -v pm2 &> /dev/null; then
    echo "正在安装 pm2..."
    npm install -g pm2
fi

# 使用 pm2 启动服务
echo "停止已存在的服务..."
pm2 stop scraper-service 2>/dev/null || true
pm2 delete scraper-service 2>/dev/null || true

echo "启动新服务..."
pm2 start dist/app.js \
    --name "scraper-service" \
    --time \
    --log "$PWD/logs/scraper.log" \
    --error "$PWD/logs/scraper.error.log" \
    --max-memory-restart 1G \
    --env NODE_ENV=production \
    --env PORT=13002 \
    --env PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"

# 保存 pm2 配置，确保系统重启后自动启动
pm2 save
pm2 startup
EOL

chmod +x "$TARGET_DIR/start.sh"

# 5. 安装生产依赖
echo "安装生产依赖..."
cd "$TARGET_DIR"
yarn install --production

# 安装 playwright 和相关依赖
echo "安装 Playwright 和相关依赖..."
yarn add playwright-core ws
npx playwright install chromium

# 确保 pm2 已安装
if ! command -v pm2 &> /dev/null; then
    echo "安装 pm2..."
    npm install -g pm2
fi

echo "发布完成！"
echo "服务目录: $TARGET_DIR"
echo "使用方法:"
echo "1. 启动服务: $TARGET_DIR/start.sh"
echo "2. 查看日志: tail -f $LOGS_DIR/scraper.log"
