# Scraper Service

一个基于 Node.js 的网页内容抓取服务，支持多种抓取方式。

## 发布部署

### 1. 执行发布脚本

```bash
./publish.sh
```

发布脚本会：
- 编译项目
- 创建服务目录
- 安装依赖
- 配置启动脚本
- 安装必要的系统依赖

### 2. 服务管理
发布完成后，可以使用以下命令管理服务：

```bash
# 启动服务
./start.sh

# 查看服务状态
pm2 status

# 查看日志
pm2 logs scraper-service

# 停止服务
pm2 stop scraper-service

# 重启服务
pm2 restart scraper-service
 ```

### 3. 服务配置
服务默认配置：

- 端口：13002
- 日志目录： ~/scraper-service/logs/
- 进程管理：pm2
- 内存限制：1GB
### 4. 注意事项
- 确保系统已安装 Node.js 和 yarn
- 需要 Chrome 浏览器支持
- 首次启动会自动安装必要的依赖
- 服务会在系统重启后自动启动
## API 使用
服务启动后，可以通过以下接口访问：

```bash
curl -X POST http://localhost:13002/scrape \
  -H "Content-Type: application/json" \
  -d '{
    "url": "要抓取的网址",
    "useAxios": true,
    "usePlaywright": true,
    "useFirecrawl": false,
    "useJina": false
  }'
 ```