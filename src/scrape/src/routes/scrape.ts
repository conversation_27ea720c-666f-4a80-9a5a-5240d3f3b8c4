import express from 'express';
import { WebScraper } from '../scraper';
import logger from '../utils/logger';

const router = express.Router();

router.post('/', async (req, res) => {
  try {
    const { 
      url,
      useAxios = false, 
      usePlaywright = true,
      useFirecrawl = false,
      useJina = false,
      cookies
    } = req.body;
    
    if (!url) {
      return res.status(400).json({ message: '请提供要抓取的 URL' });
    }

    const scraper = WebScraper.getInstance();
    const result = await scraper.fetchContent(url, {
      useAxios,
      usePlaywright,
      useFirecrawl,
      useJina,
      cookies
    });
    
    return res.status(200).json(result);
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    logger.error(`抓取失败: ${errorMessage}`);
    return res.status(500).json({
      message: errorMessage
    });
  }
});

export default router;