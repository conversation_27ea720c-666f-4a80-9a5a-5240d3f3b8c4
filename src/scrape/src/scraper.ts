import { chromium } from 'playwright';
import logger from './utils/logger';
import axios from 'axios';
import { htmlToText } from 'html-to-text';
import pdfParse from 'pdf-parse';
import FirecrawlApp, { ScrapeResponse, FirecrawlDocument } from '@mendable/firecrawl-js';
import * as fs from 'fs';

export interface ScrapedContent {
  title: string;
  content: string;
  url: string;
  timestamp: string;
  tool?: 'axios' | 'firecrawl' | 'jina' | 'playwright' | 'error';
}

export class WebScraper {
  private static instance: WebScraper;
  private browserPromise: Promise<any> | null = null;
  private firecrawlApp: FirecrawlApp;

  private constructor() {
    this.firecrawlApp = new FirecrawlApp({
      apiKey: process.env.FIRECRAWL_API_KEY || 'fc-c236b678541f43d282cbcafba258d82d'
    });
  }

  public static getInstance(): WebScraper {
    if (!WebScraper.instance) {
      WebScraper.instance = new WebScraper();
    }
    return WebScraper.instance;
  }

  private getUBlockPath(): string {
    const extensionPath = `${process.cwd()}/extension/uBlock`;
    if (!fs.existsSync(extensionPath)) {
      throw new Error('uBlock Origin extension not found');
    }
    return extensionPath;
  }

  private async initBrowser() {
    if (!this.browserPromise) {
      // const uBlockPath = this.getUBlockPath();
      
      this.browserPromise = chromium.launch({
        headless: false,
        executablePath: process.env.PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH,
        args: [
          '--disable-blink-features=AutomationControlled',
          '--disable-features=IsolateOrigins,site-per-process',
          '--disable-dev-shm-usage',
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-infobars',
          '--window-position=0,0',
          '--window-size=1,1',  // 设置窗口大小为最小
          '--start-minimized',  // 启动时最小化
          '--ignore-certifcate-errors',
          '--ignore-certifcate-errors-spki-list',
          '--disable-blink-features=AutomationControlled',
          '--disable-web-security',
          '--no-first-run',
          '--no-default-browser-check',
          '--disable-translate',
          '--metrics-recording-only',
          '--disable-sync',
          '--no-experiments',
          '--no-pings',
          // 添加广告屏蔽相关参数
          '--disable-notifications',
          '--disable-popup-blocking',
          // `--disable-extensions-except=${uBlockPath}`,
          // `--load-extension=${uBlockPath}`,
          '--block-new-web-contents',
          '--disable-background-networking'
        ]
      });
    }
    return this.browserPromise;
  }

  private async getPage() {
    const browser = await this.initBrowser();
    const context = await browser.newContext({
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      viewport: { width: 1920, height: 1080 },
      ignoreHTTPSErrors: true,
      javaScriptEnabled: true,
      locale: 'en-US',
      timezoneId: 'America/New_York',
      permissions: ['geolocation', 'notifications'],
      geolocation: { latitude: 40.7128, longitude: -74.0060 },
      colorScheme: 'light',
      deviceScaleFactor: 2,
      isMobile: false,
      hasTouch: false
    });
    
    // 增强反检测
    await context.addInitScript(() => {
      // 修改 webdriver 标记
      Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
      
      // @ts-ignore
      window.navigator.chrome = {
        app: {
          InstallState: {
            DISABLED: 'DISABLED',
            INSTALLED: 'INSTALLED',
            NOT_INSTALLED: 'NOT_INSTALLED'
          },
          RunningState: {
            CANNOT_RUN: 'CANNOT_RUN',
            READY_TO_RUN: 'READY_TO_RUN',
            RUNNING: 'RUNNING'
          },
          getDetails: function() {},
          getIsInstalled: function() {},
          installState: function() {},
          isInstalled: function() {},
          runningState: function() {}
        },
        runtime: {
          OnInstalledReason: {
            CHROME_UPDATE: 'chrome_update',
            INSTALL: 'install',
            SHARED_MODULE_UPDATE: 'shared_module_update',
            UPDATE: 'update'
          },
          PlatformArch: {
            ARM: 'arm',
            ARM64: 'arm64',
            MIPS: 'mips',
            MIPS64: 'mips64',
            X86_32: 'x86-32',
            X86_64: 'x86-64'
          },
          PlatformNaclArch: {
            ARM: 'arm',
            MIPS: 'mips',
            MIPS64: 'mips64',
            X86_32: 'x86-32',
            X86_64: 'x86-64'
          },
          PlatformOs: {
            ANDROID: 'android',
            CROS: 'cros',
            LINUX: 'linux',
            MAC: 'mac',
            OPENBSD: 'openbsd',
            WIN: 'win'
          },
          RequestUpdateCheckStatus: {
            NO_UPDATE: 'no_update',
            THROTTLED: 'throttled',
            UPDATE_AVAILABLE: 'update_available'
          }
        }
      };
      
      // 添加语言和平台信息
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en']
      });
      
      Object.defineProperty(navigator, 'plugins', {
        get: () => [
          {
            name: 'Chrome PDF Plugin',
            filename: 'internal-pdf-viewer',
            description: 'Portable Document Format'
          }
        ]
      });

      // 添加音频指纹
      Object.defineProperty(window, 'AudioContext', {
        get: () => class MockAudioContext {
          destination = { maxChannelCount: 2 }
          sampleRate = 44100
          state = 'running'

          createOscillator() {
            return { frequency: { value: 0 } };
          }
          createAnalyser() {
            return {
              frequencyBinCount: 1024,
              getByteFrequencyData: () => {},
              getFloatFrequencyData: () => {}
            };
          }
          createGain() {
            return {
              gain: { value: 1, setValueAtTime: () => {} },
              connect: () => {},
              disconnect: () => {}
            };
          }
        }
      });

      // 添加字体指纹
      Object.defineProperty(window, 'FontFace', {
        get: () => class MockFontFace {
          constructor(family: string, source: string) {
            this.family = family;
            this.source = source;
          }
          family: string
          source: string
          load() { return Promise.resolve(this); }
          loaded = Promise.resolve(this)
        }
      });
      
      Object.defineProperty(window, 'fonts', {
        get: () => ({
          ready: Promise.resolve(),
          check: () => Promise.resolve(true),
          load: () => Promise.resolve([]),
          add: () => {},
          clear: () => {},
          delete: () => {},
          forEach: () => {},
          entries: () => [],
          keys: () => [],
          values: () => [],
          has: () => false
        })
      });

      // 添加 WebGL 指纹
      const getParameter = WebGLRenderingContext.prototype.getParameter;
      WebGLRenderingContext.prototype.getParameter = function(parameter) {
        // 模拟常见的 WebGL 参数
        const fakeParams: Record<number, string> = {
          37445: 'Intel Inc.',
          37446: 'Intel Iris Graphics'
        };
        return fakeParams[parameter] || getParameter.call(this, parameter);
      };

      // 添加 canvas 指纹
      const originalGetContext = HTMLCanvasElement.prototype.getContext;
      HTMLCanvasElement.prototype.getContext = function(
        this: HTMLCanvasElement,
        contextId: '2d' | 'bitmaprenderer' | 'webgl' | 'webgl2',
        options?: CanvasRenderingContext2DSettings | ImageBitmapRenderingContextSettings | WebGLContextAttributes
      ) {
        const context = originalGetContext.call(this, contextId, options);
        if (context && contextId === '2d') {
          context.toString = () => '[object CanvasRenderingContext2D]';
        }
        return context;
      } as typeof HTMLCanvasElement.prototype.getContext;
    });
    
    // 直接返回新页面，不存储在 pagePromise 中
    return context.newPage();
  }

  private cleanContent(content: string): string {
    return content
      .replace(/data:image\/[^;]+;base64,[^\s]+/g, '') // 移除 base64 图片
      .replace(/[ \t]+/g, ' ')  // 只处理水平空白字符
      .replace(/\n{3,}/g, '\n\n')  // 将连续3个以上换行符替换为2个
      .trim();
  }

  public async close() {
    if (this.browserPromise) {
      const browser = await this.browserPromise;
      await browser.close();
      this.browserPromise = null;
    }
  }

  private isValidContent(content: string): boolean {
    if (!content || content.trim().length < 100) {
      return false;
    }

    const failureMarkers = [
      'Continue reading',
      'More for You',
      'login.microsoftonline.com',
      'Expand article logo',
      'Just a moment...',
      'Verify you are human',
      'Access denied',
      'Please enable JavaScript',
      'Robot check',
      'Subscribe to read'
    ];

    return !failureMarkers.some(marker => content.includes(marker));
  }

  public async fetchWithPlaywright(url: string, cookies?: { name: string; value: string; domain: string }[]): Promise<ScrapedContent> {
    const page = await this.getPage();

    try {
      // 先设置浏览器特征
      await page.setExtraHTTPHeaders({
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'DNT': '1',
        'Pragma': 'no-cache',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"macOS"',
        'Sec-Ch-Ua-Arch': '"arm"',
        'Sec-Ch-Ua-Full-Version': '"120.0.6099.129"',
        'Sec-Ch-Ua-Platform-Version': '"14.0.0"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1'
      });

      // 再设置 cookies
      if (cookies && cookies.length > 0) {
        await page.context().addCookies(cookies.map(cookie => ({
          ...cookie,
          path: '/',
          secure: true,
          sameSite: 'None' as const
        })));
      }

      // 访问页面并等待关键元素
      await page.goto(url);
      
      // 等待页面主要内容加载完成
      await Promise.race([
        // 等待特定选择器出现
        page.waitForSelector('article, [data-component="StoryBody"], .news-content', { timeout: 30000 }),
        // 等待网络请求基本完成
        page.waitForLoadState('domcontentloaded'),
        // 等待页面不再有网络连接
        page.waitForLoadState('networkidle', { timeout: 45000 })
      ]);

      // 获取页面标题
      const title = await page.title();

      // 检查是否遇到反爬验证
      if (title.includes('Just a moment...') || await page.content().then((content: string) => content.includes('Verify you are human'))) {
        throw new Error('遇到反爬虫验证，需要人工验证');
      }

      // 注入 isValidContent 函数
      await page.addScriptTag({
        content: `
          window.isValidContent = ${this.isValidContent.toString()}
        `
      });

      // 提取发布时间和主要内容
      const { content, publishedTime } = await page.evaluate(() => {
        // 移除干扰元素
        const removeSelectors = ['header', 'footer', 'nav', 'script', 'style', 'iframe', 'ads'];
        removeSelectors.forEach(selector => {
          document.querySelectorAll(selector).forEach(el => el.remove());
        });

        // 尝试获取发布时间
        const publishedTime = 
          document.querySelector('meta[property="article:published_time"]')?.getAttribute('content') ||
          document.querySelector('meta[name="publishedDate"]')?.getAttribute('content') ||
          document.querySelector('meta[name="date"]')?.getAttribute('content') ||
          document.querySelector('time[datetime]')?.getAttribute('datetime') ||
          document.querySelector('[class*="time"], [class*="date"]')?.textContent ||
          new Date().toISOString();

        // 获取主要内容区域
        const selectors = [
          '.news-content',
          '.content',
          '#content',
          'article', 
          'main'  // 通用标签放在后面
        ];
        
        let mainContent = null;
        for (const selector of selectors) {
          mainContent = document.querySelector(selector);
          const content = mainContent?.textContent?.trim() || '';
          // @ts-ignore
          if (window.isValidContent?.(content)) {
            break;
          }
        }
        
        const extractedContent = mainContent ? mainContent.textContent : document.body.textContent;

        return {
          content: extractedContent?.replace(/\s+/g, ' ').trim() || '',
          publishedTime
        };
      });

      if (!this.isValidContent(content)) {
        throw new Error('页面内容为空、过短或包含无效内容');
      }

      logger.info(`Playwright scraping success: ${url}`);

      return {
        title,
        content: this.cleanContent(content),
        url,
        timestamp: new Date().toISOString(),
        tool: 'playwright'
      };

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Playwright scraping failed: ${url}: ${errorMessage}`);
      
      // 出错时关闭页面
      await page.close();
      
      throw error instanceof Error ? error : new Error(errorMessage);
    } finally {
      // 确保页面被关闭
      await page.close();
    }
  }

  private async fetchWithAxios(url: string, cookies?: { name: string; value: string; domain: string }[]): Promise<ScrapedContent> {
    const cookieHeader = cookies?.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');

    const res = await axios.get(url, {
      responseType: 'arraybuffer',
      timeout: 15000,
      maxContentLength: 10 * 1024 * 1024,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      },
      validateStatus: (status) => status >= 200 && status < 300,
      maxRedirects: 5,
      ...(cookieHeader ? { 'Cookie': cookieHeader } : {})
    });

    if (res.headers['content-type']?.includes('application/pdf')) {
      const pdfText = await pdfParse(res.data);
      return {
        title: 'PDF Document',
        content: pdfText.text,
        url,
        timestamp: new Date().toISOString()
      };
    }

    const htmlContent = res.data.toString('utf8');
            
    if (htmlContent.includes('Just a moment...') || 
        htmlContent.includes('Verify you are human')) {
      throw new Error('遇到反爬虫验证，需要人工验证');
    }

    const content = htmlToText(htmlContent, {
      selectors: [{ selector: 'a', options: { ignoreHref: true } }],
    });
    
    const title = htmlContent.match(/<title>(.*?)<\/title>/)?.[1] || url;
    if (!this.isValidContent(content) || title.includes('Subscribe to read')) {
      throw new Error('页面内容为空、过短或包含无效内容');
    }

    logger.info(`Axios scraping success: ${url}`);
    
    return {
      title,
      content: this.cleanContent(content),
      url,
      timestamp: new Date().toISOString(),
      tool: 'axios'
    };
  }
  
  private async fetchWithJina(url: string): Promise<ScrapedContent> {
      try {
        const response = await axios.post('https://r.jina.ai/', 
          { url },
          {
            timeout: 35000,
            headers: {
              'Authorization': `Bearer jina_8e6c2bd3c784401cac6f3006931c133cydplqg_10kTiVu_jFMR3gGQvnqnc`,
              'Content-Type': 'application/json',
              'X-Remove-Selector': 'header, footer, nav, .ad, #ad, script, style, iframe',
              'X-Retain-Images': 'none',
              'X-Target-Selector': 'main, article, .content, #content, body',
              'X-Timeout': '30000',
              // 添加请求元数据
              'X-Extract-Metadata': 'true'
            }
          }
        );
  
        const { title, content } = response.data;
        
        if (!this.isValidContent(content)) {
          throw new Error('页面内容为空、过短或包含无效内容');
        }
  
        logger.info(`Jina scraping success: ${url}`);
  
        return {
          title: title || url,
          content: this.cleanContent(content),
          url,
          timestamp: new Date().toISOString(),
          tool: 'jina' 
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger.error(`Jina scraping failed: ${url}: ${errorMessage}`);
        throw error;
      }
    }
  
    private async fetchWithFirecrawl(url: string): Promise<ScrapedContent> {
      try {
        const scrapeResult = await this.firecrawlApp.scrapeUrl(url, {
          formats: ['markdown'],
          onlyMainContent: true,
          removeBase64Images: true,
          blockAds: true
        }) as ScrapeResponse;

        if (!scrapeResult.success) {
          throw new Error(`Firecrawl scraping failed: ${scrapeResult.error}`);
        }

        const { markdown = '', metadata = {} } = scrapeResult;
        const { title } = metadata;
        
        if (!this.isValidContent(markdown)) {
          throw new Error('页面内容为空、过短或包含无效内容');
        }

        logger.info(`Firecrawl scraping success: ${url}`);

        return {
          title: title || url,
          content: this.cleanContent(markdown),
          url,
          timestamp: new Date().toISOString(),
          tool: 'firecrawl'
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger.error(`Firecrawl scraping failed: ${url}: ${errorMessage}`);
        throw error;
      }
    }

    public async fetchContent(url: string, options: { 
      useAxios?: boolean;
      usePlaywright?: boolean;
      useFirecrawl?: boolean;
      useJina?: boolean;
      cookies?: { name: string; value: string; domain: string }[];
    } = {}): Promise<ScrapedContent> {
      const { 
        useAxios = true,
        usePlaywright = true,
        useFirecrawl = true,
        useJina = false,
        cookies
      } = options;
  
      try {
        // 首先尝试使用 Axios
        if (useAxios) {
          try {
            return await this.fetchWithAxios(url, cookies);
          } catch (axiosError) {
            const nextMethod = usePlaywright ? 'Playwright' : 
                             useFirecrawl ? 'Firecrawl' : 
                             useJina ? 'Jina' : null;
            logger.info(`Axios failed${nextMethod ? `, trying ${nextMethod}` : ', no more methods available'}: ${url}`);
          }
        }

        // 然后尝试使用 Playwright
        if (usePlaywright) {
          try {
            return await this.fetchWithPlaywright(url, cookies);
          } catch (playwrightError) {
            const nextMethod = useFirecrawl ? 'Firecrawl' : 
                             useJina ? 'Jina' : null;
            logger.info(`Playwright failed${nextMethod ? `, trying ${nextMethod}` : ', no more methods available'}: ${url}`);
          }
        }

        // 接着尝试使用 Firecrawl
        if (useFirecrawl) {
          try {
            return await this.fetchWithFirecrawl(url);
          } catch (firecrawlError) {
            const nextMethod = useJina ? 'Jina' : null;
            logger.info(`Firecrawl failed${nextMethod ? `, trying ${nextMethod}` : ', no more methods available'}: ${url}`);
          }
        }

        // 最后尝试使用 Jina
        if (useJina) {
          try {
            return await this.fetchWithJina(url);
          } catch (jinaError) {
            logger.info(`Jina failed, no more methods available: ${url}`);
          }
        }
  
        throw new Error('所有启用的抓取方法都失败了');
      } catch (error) {
        throw error;
      }
    }
}