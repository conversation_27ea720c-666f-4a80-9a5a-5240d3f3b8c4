import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';

const configFileName = 'config.yaml';

interface Config {
  GENERAL: {
    PORT: number;
    SIMILARITY_MEASURE: string;
    KEEP_ALIVE: string;
  };
  API_KEYS: {
    OPENAI: string;
    GEMINI: string;
    TRANSLATE: string;
  };
  API_ENDPOINTS: {
    SEARXNG: string;
    OLLAMA: string;
    SCRAPER: string;
  };
  CUSTOM_MODEL: {
    CUSTOM_BASE_URL: string;
    CUSTOM_CHAT_MODEL: string;
    CUSTOM_EMBEDDING_MODEL: string;
    CUSTOM_SUMMARIZER_MODEL: string;
    CUSTOM_TRANSLATOR_MODEL: string;
  }
}

type RecursivePartial<T> = {
  [P in keyof T]?: RecursivePartial<T[P]>;
};

const loadConfig = () => {
  try {
    return yaml.load(
      fs.readFileSync(path.join(__dirname, `../${configFileName}`), 'utf-8'),
    ) as Config;
  } catch (error) {
    console.error('配置文件解析错误:', error);
  }
};

export const updateConfig = (config: RecursivePartial<Config>) => {
  const currentConfig = loadConfig();

  for (const key in currentConfig) {
    if (!config[key]) config[key] = {};

    if (typeof currentConfig[key] === 'object' && currentConfig[key] !== null) {
      for (const nestedKey in currentConfig[key]) {
        if (
          !config[key][nestedKey] &&
          currentConfig[key][nestedKey] &&
          config[key][nestedKey] !== ''
        ) {
          config[key][nestedKey] = currentConfig[key][nestedKey];
        }
      }
    } else if (currentConfig[key] && config[key] !== '') {
      config[key] = currentConfig[key];
    }
  }

  fs.writeFileSync(
    path.join(__dirname, `../${configFileName}`),
    yaml.dump(config, { indent: 2 }),
  );
};

export const getPort = () => loadConfig().GENERAL.PORT;

export const getSimilarityMeasure = () =>
  loadConfig().GENERAL.SIMILARITY_MEASURE;

export const getKeepAlive = () => loadConfig().GENERAL.KEEP_ALIVE;

export const getOpenaiApiKey = () => loadConfig().API_KEYS.OPENAI;

export const getGeminiApiKey = () => loadConfig().API_KEYS.GEMINI;

export const getSearxngApiEndpoint = () =>
  process.env.SEARXNG_API_URL || loadConfig().API_ENDPOINTS.SEARXNG;

export const getOllamaApiEndpoint = () => loadConfig().API_ENDPOINTS.OLLAMA;

// 获取抓取服务的API端点
export const getScraperApiEndpoint = () => loadConfig().API_ENDPOINTS.SCRAPER;

// 获取自定义 Base URL
export const getCustomBaseURL = () => {
  return loadConfig().CUSTOM_MODEL?.CUSTOM_BASE_URL || '';
};

// 获取自定义聊天模型
export const getCustomChatModel = () => {
  return loadConfig().CUSTOM_MODEL?.CUSTOM_CHAT_MODEL || '';
};

// 获取自定义嵌入模型
export const getCustomEmbeddingModel = () => {
  return loadConfig().CUSTOM_MODEL?.CUSTOM_EMBEDDING_MODEL || '';
};

// 获取自定义总结模型
export const getCustomSummarizerModel = () => {
  return loadConfig().CUSTOM_MODEL?.CUSTOM_SUMMARIZER_MODEL || '';
};

export const getCustomTranslatorModel = () => {
  return loadConfig().CUSTOM_MODEL?.CUSTOM_TRANSLATOR_MODEL || '';
};

export const getTranslateApiKey = () => {
  return loadConfig().API_KEYS?.TRANSLATE || '';
};