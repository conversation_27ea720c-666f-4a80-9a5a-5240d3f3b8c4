import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { ConversationSummaryMemory } from 'langchain/memory';
import { LRUCache } from 'lru-cache';
import logger from './logger';

type HistoryMessage = [string, string];

// 创建全局 LRU 缓存
const summaryCache = new LRUCache<string, string>({
  max: 500,  // 最多缓存500个摘要
  ttl: 1000 * 60 * 60 * 24,  // 24小时过期
});

// 生成缓存key需要考虑消息的顺序
const getCacheKey = (messages: HistoryMessage[]): string => {
  return messages
    .map(([role, content]) => `${role}:${content.slice(0, 50)}`)
    .join('|');
};

const createMemory = (llm: BaseChatModel) => {
  return new ConversationSummaryMemory({
    llm: llm as any,
    returnMessages: true,
    inputKey: "input",
    outputKey: "output",
  });
};

export const summarizeHistoryWithCache = async (
  messages: HistoryMessage[],
  summarizerLLM: BaseChatModel,
  maxMessages: number = 5,  // 获取最近的2组消息（4条）记录 + 最后一个问题
) => {
  try {
    if (!messages?.length || messages.length <= maxMessages) return messages;

    const recentMessages = messages.slice(-maxMessages);
    const oldMessages = messages.slice(0, -maxMessages);
    
    // 确保 oldMessages 是偶数条消息（完整的对话）
    const isOddLength = oldMessages.length % 2 === 1;
    const messagesToSummarize = isOddLength ? oldMessages.slice(0, -1) : oldMessages;
    
    const allHistoryCacheKey = getCacheKey(messagesToSummarize);
    const cachedFullSummary = summaryCache.get(allHistoryCacheKey);

    if (cachedFullSummary) {
      return [['assistant', `历史对话要点：${cachedFullSummary}`], ...recentMessages];
    }

    const memory = createMemory(summarizerLLM);
    let previousSummary = '';
    
    // 按对处理消息
    for (let i = 0; i < messagesToSummarize.length; i += 2) {
      const humanMessage = messagesToSummarize[i];
      const aiMessage = messagesToSummarize[i + 1];
      
      if (humanMessage && aiMessage) {
        const input = previousSummary 
          ? `之前的对话要点：${previousSummary}\n\n${humanMessage[1]}`
          : humanMessage[1];
              
        await memory.saveContext(
          { input },
          { output: aiMessage[1] }
        );
        
        const { history } = await memory.loadMemoryVariables({});
        previousSummary = Array.isArray(history) 
          ? (history[history.length - 1]?.content || '')
          : history;
      }
    }

    const { history } = await memory.loadMemoryVariables({});
    const summary = Array.isArray(history) 
      ? (history[history.length - 1]?.content || '')
      : history;

    // 存入缓存时进行验证
    if (summary && summary.trim()) {
      const cacheKeys = [
        getCacheKey(oldMessages),
        allHistoryCacheKey
      ].filter(Boolean);
      
      for (const key of cacheKeys) {
        if (key) summaryCache.set(key, summary);
      }
    } else {
      logger.warn('生成的摘要为空，使用原始消息');
      return messages.slice(-maxMessages);
    }
    
    return [['assistant', `历史对话要点：${summary}`], ...recentMessages];
    
  } catch (error) {
    logger.error('历史消息总结失败:', error);
    return messages.slice(-maxMessages);
  }
};

// 简化版本 summarizeHistory
export const summarizeHistory = async (
  messages: HistoryMessage[],
  summarizerLLM: BaseChatModel,
  maxMessages: number = 5,  // 获取最近的2组消息（4条）记录 + 最后一个问题
) => {
  try {
    if (!messages?.length) return messages;
    return messages.slice(-maxMessages);
  } catch (error) {
    logger.error('获取历史消息失败:', error);
    return messages;
  }
};