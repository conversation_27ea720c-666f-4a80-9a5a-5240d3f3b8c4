import { getSummarizerModel } from '../lib/providers';
import { getDocumentsFromLinks } from './documents';
import { generateSummaryPrompt } from '../prompts/summarizer';

export async function summarize(text: string, url?: string): Promise<string> {
  try {
    const model = await getSummarizerModel();
    
    let contentToSummarize = text;
    
    if (url) {
      try {
        const docs = await getDocumentsFromLinks({ links: [url] });
        if (docs && docs.length > 0) {
          const content = docs[0].pageContent;
          if (
            content.length > 100 &&
            !content.includes('Privacy Policy') &&
            !content.includes('Contact Us') &&
            !content.includes('Terms of Service') &&
            !content.includes('Accessibility Statement') &&
            !content.includes('data:image') &&
            !/^\s*$/.test(content)
          ) {
            contentToSummarize = content;
          }
        }
      } catch (error) {
        console.error('Error fetching content from URL:', error);
      }
    }

    const response = await model.invoke(generateSummaryPrompt(contentToSummarize));
    return response.content.replace(/<[^>]*>/g, '').replace(/[*_`]/g, '').trim();
  } catch (error) {
    console.error('Summarization error:', error);
    return '';
  }
}