import dot from 'compute-dot';
import cosineSimilarity from 'compute-cosine-similarity';
import { getSimilarityMeasure } from '../config';
import { getSummarizerModel, getEmbeddingModel } from '../lib/providers';

// 使用 LLM 进行相似度检测
const checkSimilarityWithLLM = async (text1: string, text2: string): Promise<number> => {
  try {
    const model = await getSummarizerModel();
    const prompt = `请判断以下两段文本的相似度，只需要返回一个0到1之间的数字，表示相似度：
文本1：${text1}
文本2：${text2}
请直接返回相似度数值（0-1之间），例如：0.85`;

    const response = await model.invoke(prompt);
    const similarity = parseFloat(response) || 0;
    return similarity;
  } catch (error) {
    console.error('LLM similarity check failed:', error);
    return 0;
  }
};

// 计算向量相似度
const computeSimilarity = (x: number[], y: number[]): number => {
  const similarityMeasure = getSimilarityMeasure();

  if (similarityMeasure === 'cosine') {
    return cosineSimilarity(x, y);
  } else if (similarityMeasure === 'dot') {
    return dot(x, y);
  }

  throw new Error('Invalid similarity measure');
};

// 计算文本相似度
const computeTextSimilarity = async (
  text1: string, 
  text2: string, 
  method: 'vector' | 'llm' = 'vector'
): Promise<number> => {
  // 如果内容长度相差太大，直接判定为不相似
  if (Math.abs(text1.length - text2.length) > 100) {
    return 0;
  }

  try {
    if (method === 'llm') {
      return await checkSimilarityWithLLM(text1, text2);
    }

    const embeddings = await getEmbeddingModel();
    
    // 获取文本的向量表示
    const [embedding1, embedding2] = await Promise.all([
      embeddings.embedQuery(text1),
      embeddings.embedQuery(text2)
    ]);

    return computeSimilarity(embedding1, embedding2);
  } catch (error) {
    console.error('Error computing text similarity:', error);
    // 如果向量化失败，回退到基础的 Jaccard 相似度
    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }
};

export { computeSimilarity, computeTextSimilarity };