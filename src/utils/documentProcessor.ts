import { PDFLoader } from '@langchain/community/document_loaders/fs/pdf';
import { DocxLoader } from '@langchain/community/document_loaders/fs/docx';
import { Document } from 'langchain/document';
import fs from 'fs';

export interface DocumentProcessorResult {
  docs: Document[];
  metadata: {
    title: string;
    type: string;
    pageCount?: number;
  };
}

export async function processDocument(filePath: string, fileName: string): Promise<DocumentProcessorResult> {
  const fileExtension = fileName.split('.').pop()?.toLowerCase();
  let docs: Document[] = [];
  let metadata = {
    title: fileName,
    type: fileExtension || 'unknown',
    pageCount: 0
  };

  try {
    switch (fileExtension) {
      case 'pdf':
        const pdfLoader = new PDFLoader(filePath);
        docs = await pdfLoader.load();
        metadata.pageCount = docs.length;
        break;

      case 'docx':
        const docxLoader = new DocxLoader(filePath);
        docs = await docxLoader.load();
        metadata.pageCount = 1; // Word文档通常作为单个文档处理
        break;

      case 'txt':
        const text = fs.readFileSync(filePath, 'utf-8');
        docs = [
          new Document({
            pageContent: text,
            metadata: {
              title: fileName,
              source: filePath,
            },
          }),
        ];
        metadata.pageCount = 1;
        break;

      default:
        throw new Error(`Unsupported file type: ${fileExtension}`);
    }

    return { docs, metadata };
  } catch (error) {
    throw new Error(`Error processing document: ${error.message}`);
  }
}