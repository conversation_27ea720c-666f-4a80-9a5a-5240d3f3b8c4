import { getTranslatorModel } from '../lib/providers';

export async function translate(text: string): Promise<string> {
  try {
    const model = await getTranslatorModel();
    const response = await model.invoke(`
      你是一个专业的翻译专家，需要将以下内容翻译为简体中文。你在翻译时要确保符合中文语言习惯，你可以调整语气和风格，并考虑到某些词语的文化内涵和地区差异。同时作为翻译家，需将原文翻译成具有信达雅标准的译文。"信" 即忠实于原文的内容与意图；"达" 意味着译文应通顺易懂，表达清晰；"雅" 则追求译文的文化审美和语言的优美。目标是创作出既忠于原作精神，又符合目标语言文化和读者审美的翻译。
      以下是需要翻译的内容：
      
      <text>
      ${text}
      </text>
      
      请直接提供中文翻译结果，无需解释或添加任何标记。
      遇到无法翻译的词或句直接返回原词或句子。
    `);

    // 清理可能的标签和特殊符号
    return response.content.replace(/<[^>]*>/g, '').replace(/[*_`]/g, '').trim();
  } catch (error) {
    console.error('Translation error:', error);
    return text;  // 翻译失败时返回原文
  }
}