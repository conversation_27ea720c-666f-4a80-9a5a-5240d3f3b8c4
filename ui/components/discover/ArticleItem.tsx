import { cn } from '@/lib/utils';
import { Discover } from '@/types/discover';
import { useState, useEffect } from 'react';
import { CheckCircle2, Loader2, XCircle } from 'lucide-react';

interface ArticleItemProps {
  item: Discover;
  index: number;
  expandedItems: number[];
  toggleExpand: (index: number) => void;
  onSelect: (item: Discover) => void;
}

export const ArticleItem = ({ item, index, expandedItems, toggleExpand, onSelect }: ArticleItemProps) => {
  const [isRead, setIsRead] = useState(!!item.chatId);

  useEffect(() => {
    setIsRead(!!item.chatId);
  }, [item.chatId]);

  return (
    <div
      onClick={() => onSelect(item)}
      className={cn(
        "group flex flex-col gap-2 p-3 rounded-2xl shadow-sm bg-gradient-to-br from-white/90 to-blue-50/60 dark:from-[#23263a] dark:to-[#1a1c2b] border border-black/5 dark:border-white/10 transition-all duration-200 cursor-pointer relative overflow-hidden",
        "hover:shadow-lg hover:-translate-y-0.5 hover:scale-[1.015] active:scale-100",
        isRead
          ? "ring-1 ring-blue-200 dark:ring-blue-700/40"
          : "ring-1 ring-gray-100 dark:ring-gray-800/60"
      )}
      style={{ minHeight: 96 }}
    >

      <div className="flex flex-row gap-3 items-center">
        {item.thumbnail && (
          <div className="flex-shrink-0">
            <img
              src={item.thumbnail}
              alt={item.titleZh}
              className="w-20 h-20 object-cover rounded-xl shadow-md border border-black/10 dark:border-white/10 bg-white/40 dark:bg-black/20"
              onError={(e) => {
                (e.target as HTMLImageElement).style.display = 'none';
              }}
            />
          </div>
        )}

        <div className="flex flex-col flex-1 min-w-0">
          <div className={cn(
            "font-bold text-[1.08rem] leading-snug text-black dark:text-white flex items-center gap-2",
            !expandedItems.includes(index) && "line-clamp-2"
          )}>
            {item.titleZh}
            {item.titleZh !== item.title && (
              <span className="ml-2 text-xs text-black/40 dark:text-white/40 font-normal">
                原标题：{item.title}
              </span>
            )}
          </div>
          <div className="mt-1.5">
            <p className={cn(
              "text-black/80 dark:text-white/80 text-sm leading-relaxed",
              !expandedItems.includes(index) && "line-clamp-2"
            )}>
              {item.contentZh || item.content}
            </p>
          </div>
          <div className="flex items-end mt-2">
            <div className="flex items-center gap-2 min-w-0">
              <img
                src={`https://s2.googleusercontent.com/s2/favicons?domain_url=${item.url}`}
                width={18}
                height={18}
                alt="favicon"
                className="rounded-md h-4 w-4 bg-white/80 dark:bg-black/30 border border-black/10 dark:border-white/10"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
              <span className="text-xs text-black/60 dark:text-white/60 font-medium truncate max-w-[90px]">
                {item.url.replace(/.+\/\/|www.|\..+/g, '')}
              </span>
              <span className={cn(
                "ml-1 text-[10px] px-1.5 py-0.5 rounded-full font-bold inline-flex items-center gap-1 border",
                item.fetchStatus === 'success' && "bg-green-100 text-green-700 border-green-300 dark:bg-green-900/40 dark:text-green-300 dark:border-green-700/40",
                item.fetchStatus === 'pending' && "bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/40 dark:text-yellow-300 dark:border-yellow-700/40",
                item.fetchStatus === 'failed' && "bg-red-100 text-red-700 border-red-300 dark:bg-red-900/40 dark:text-red-300 dark:border-red-700/40"
              )}>
                {item.fetchStatus === 'success' ? (
                  <CheckCircle2 className="w-3 h-3" />
                ) : item.fetchStatus === 'pending' ? (
                  <Loader2 className="w-3 h-3 animate-spin" />
                ) : (
                  <XCircle className="w-3 h-3" />
                )}
              </span>
              <span className="ml-2 text-xs text-black/40 dark:text-white/40 whitespace-nowrap">
                {new Date(item.publishedDate).toLocaleDateString('zh-CN')}
              </span>
            </div>
            <div className="flex items-center gap-2 ml-auto">
              {(item.title.length > 100 || item.content.length > 200) && (
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleExpand(index);
                  }}
                  className="flex items-center gap-1 text-xs font-medium px-2 py-1 rounded-md transition-colors duration-150 bg-black/5 dark:bg-white/5 hover:bg-blue-100/80 dark:hover:bg-blue-900/40 text-blue-700 dark:text-blue-300"
                >
                  {expandedItems.includes(index) ? (
                    <>
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 16 16"><path d="M12 10L8 6 4 10" strokeLinecap="round" strokeLinejoin="round"/></svg>
                      收起
                    </>
                  ) : (
                    <>
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 16 16"><path d="M4 6l4 4 4-4" strokeLinecap="round" strokeLinejoin="round"/></svg>
                      展开
                    </>
                  )}
                </button>
              )}
              <span
                className={cn(
                  "px-1 py-0.5 rounded-full text-xs shadow-sm transition-colors duration-300 select-none",
                  isRead
                    ? "bg-gradient-to-r from-green-400/90 to-green-600/90 text-white border border-green-500/40"
                    : "bg-gradient-to-r from-gray-300/80 to-gray-400/80 text-gray-800 border border-gray-400/30 dark:bg-gradient-to-r dark:from-gray-700/80 dark:to-gray-800/80 dark:text-gray-100 dark:border-gray-600/40"
                )}
              >
                {isRead ? 'Read' : 'Unread'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
