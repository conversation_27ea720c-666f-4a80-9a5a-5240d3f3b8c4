import React, { useEffect, useRef } from 'react';
import { TimerIcon, HelpCircleIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Topic } from '@/types/discover';
import { useRouter } from 'next/navigation';



interface DiscoverTopBarProps {
  topics: Topic[];
  selectedTopicId: string;
  onSelectTopic: (topicId: string, index: number) => void;
  summarizedOnly: boolean;
  importantOnly: boolean;
  onSummarizedChange: (checked: boolean) => void;
  onImportantChange: (checked: boolean) => void;
  className?: string;
}

const DiscoverTopBar: React.FC<DiscoverTopBarProps> = ({
  topics,
  selectedTopicId,
  onSelectTopic,
  summarizedOnly,
  importantOnly,
  onSummarizedChange,
  onImportantChange,
  className = '',
}) => {
  const router = useRouter();
  const tabsNavRef = useRef<HTMLElement>(null);
  const tabButtonRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

  const selectedIndex = React.useMemo(
    () => topics.findIndex(t => t.id === selectedTopicId),
    [topics, selectedTopicId]
  );

  // 当选中的主题改变时，自动滚动移动端标签到可视区域
  useEffect(() => {
    if (selectedTopicId && tabButtonRefs.current[selectedTopicId] && tabsNavRef.current) {
      const selectedButton = tabButtonRefs.current[selectedTopicId];

      // 使用 setTimeout 确保 DOM 更新完成后再滚动
      const timeoutId = setTimeout(() => {
        if (selectedButton) {
          selectedButton.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'center'
          });
        }
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [selectedTopicId]);

  // 头部筛选栏
  const Header = (
    <div
      className={cn(
        'flex flex-row items-center gap-3 px-4 py-4 bg-gradient-to-r from-white/95 via-blue-50/90 to-white/95 dark:from-[#1a1d23]/95 dark:via-blue-950/80 dark:to-[#1a1d23]/95',
        'backdrop-blur-xl border-b border-blue-200/50 dark:border-blue-800/30',
        'shadow-lg shadow-blue-100/50 dark:shadow-blue-900/20',
        'relative overflow-hidden',
        className
      )}
      style={{ minHeight: 64 }}
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-blue-500/5 dark:from-blue-400/10 dark:to-blue-400/10" />
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-300/50 to-transparent dark:via-blue-600/30" />
      {/* 左侧：图标+标题 */}
      <div className="flex items-center gap-3 flex-shrink-0 relative z-10">
        <div className="relative">
          <div className="absolute inset-0 bg-blue-500/20 dark:bg-blue-400/20 rounded-full blur-sm" />
          <TimerIcon className="relative w-6 h-6 sm:w-7 sm:h-7 flex-shrink-0 text-blue-600 dark:text-blue-400" />
        </div>
        <div className="flex items-center gap-2">
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold tracking-tight text-gray-800 dark:text-gray-100">
            新闻追踪
          </h1>
          <div className="relative flex h-2 w-2 sm:h-3 sm:w-3 flex-shrink-0">
            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-emerald-400 opacity-75"></span>
            <span className="relative inline-flex rounded-full h-full w-full bg-emerald-500"></span>
          </div>
        </div>
      </div>
      {/* 中间占位（如需扩展可用） */}
      <div className="flex-1 min-w-0"></div>
      {/* 右侧：筛选项按钮组 */}
      <div className="flex gap-1 flex-shrink-0 flex-nowrap relative z-10">
        <button
          type="button"
          className={cn(
            'flex items-center gap-1.5 px-3 py-2 rounded-xl text-xs font-medium transition-all duration-300 border backdrop-blur-sm sm:px-4 sm:py-2.5 sm:text-sm',
            'hover:scale-105 active:scale-95',
            summarizedOnly
              ? 'bg-blue-500/90 text-white border-blue-400/50 shadow-lg shadow-blue-500/25 hover:bg-blue-600/90'
              : 'bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300 border-gray-200/50 dark:border-gray-600/50 hover:bg-blue-50/80 dark:hover:bg-blue-900/30 shadow-md',
          )}
          onClick={() => onSummarizedChange(!summarizedOnly)}
        >
          <svg className="w-3.5 h-3.5 sm:w-4 sm:h-4" fill="none" stroke="currentColor" strokeWidth="2.5" viewBox="0 0 16 16">
            <path d="M3 8h10M8 3v10" />
          </svg>
          <span className="sm:hidden">总结</span>
          <span className="hidden sm:inline">已总结</span>
        </button>

        <button
          type="button"
          className={cn(
            'flex items-center gap-1.5 px-3 py-2 rounded-xl text-xs font-medium transition-all duration-300 border backdrop-blur-sm sm:px-4 sm:py-2.5 sm:text-sm',
            'hover:scale-105 active:scale-95',
            importantOnly
              ? 'bg-emerald-500/90 text-white border-emerald-400/50 shadow-lg shadow-emerald-500/25 hover:bg-emerald-600/90'
              : 'bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300 border-gray-200/50 dark:border-gray-600/50 hover:bg-emerald-50/80 dark:hover:bg-emerald-900/30 shadow-md',
          )}
          onClick={() => onImportantChange(!importantOnly)}
        >
          <svg className="w-3.5 h-3.5 sm:w-4 sm:h-4" fill="none" stroke="currentColor" strokeWidth="2.5" viewBox="0 0 16 16">
            <circle cx="8" cy="8" r="6" />
          </svg>
          <span className="sm:hidden">重点</span>
          <span className="hidden sm:inline">重点</span>
        </button>
        <button
          type="button"
          className={cn(
            'flex items-center gap-1.5 px-3 py-2 rounded-xl text-xs font-medium transition-all duration-300 border backdrop-blur-sm sm:px-4 sm:py-2.5 sm:text-sm',
            'bg-gradient-to-r from-purple-500/90 to-pink-500/90 text-white border-purple-400/50 shadow-lg shadow-purple-500/25',
            'hover:scale-105 active:scale-95 hover:from-purple-600/90 hover:to-pink-600/90'
          )}
          onClick={() => router.push('/ask')}
        >
          <HelpCircleIcon className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
          <span>提问</span>
        </button>
      </div>
    </div>
  );

  // 移动端 Tabs
  const Tabs = (
    <nav
      ref={tabsNavRef}
      className={cn(
        'flex lg:hidden w-full overflow-x-auto bg-gradient-to-r from-white/90 via-gray-50/95 to-white/90 dark:from-[#1a1d23]/90 dark:via-gray-900/95 dark:to-[#1a1d23]/90',
        'backdrop-blur-md border-b border-gray-200/50 dark:border-gray-700/50 px-3 py-2 scroll-smooth',
        'shadow-sm'
      )}
    >
      <ul className="flex flex-row space-x-3 min-w-fit">
        {topics.map((topic, idx) => (
          <li key={topic.id}>
            <button
              ref={(el) => {
                tabButtonRefs.current[topic.id] = el;
              }}
              className={cn(
                'px-4 py-2.5 rounded-xl text-sm font-medium outline-none whitespace-nowrap transition-all duration-300 border backdrop-blur-sm',
                'hover:scale-105 active:scale-95',
                selectedIndex === idx
                  ? 'bg-blue-500/90 text-white border-blue-400/50 shadow-lg shadow-blue-500/25 transform scale-105'
                  : 'bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300 border-gray-200/50 dark:border-gray-600/50 hover:bg-blue-50/80 dark:hover:bg-blue-900/30 shadow-md',
              )}
              onClick={() => onSelectTopic(topic.id, idx)}
            >
              {topic.name}
            </button>
          </li>
        ))}
      </ul>
    </nav>
  );


  return (
    <div className="fixed top-0 left-0 right-0 z-50 w-full bg-transparent backdrop-blur-md">
      {Header}
      <div className="w-full">{Tabs}</div>
    </div>
  );
};

export default DiscoverTopBar;
