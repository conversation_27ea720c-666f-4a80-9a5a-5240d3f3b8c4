import { X } from 'lucide-react';
import { cn } from '@/lib/utils';
import ChatWindow from '@/components/ChatWindow';
import DeleteChat from '../DeleteChat';
import { useEffect, useRef, useState } from 'react';

interface ChatDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  selectedChat: {
    chatId?: string;
    discoverId?: string;
    url?: string;
    title?: string;
  } | null;
  onChatIdCreated: (newChatId: string) => void;
}

export const ChatDrawer = ({ isOpen, onClose, selectedChat, onChatIdCreated }: ChatDrawerProps) => {
  const drawerRef = useRef<HTMLDivElement>(null);

  // 判断是否为移动端，仅移动端启用滑动关闭
  const [isMobile, setIsMobile] = useState(false);

  // 滑动动画状态
  const [swipeOffset, setSwipeOffset] = useState(0);
  const [isSwipeActive, setIsSwipeActive] = useState(false);
  const [showSwipeHint, setShowSwipeHint] = useState(false);
  const animationFrameRef = useRef<number | null>(null);
  const hintTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const checkMobile = () => {
      if (typeof window !== 'undefined') {
        // 1. 检查触摸能力和屏幕尺寸
        const hasTouchScreen = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        const hasCoarsePointer = window.matchMedia('(pointer: coarse)').matches;
        const isMobileWidth = window.matchMedia('(max-width: 1024px)').matches;

        // 2. 综合判断：有触摸屏且屏幕较小，或者主要输入设备是粗糙指针
        if ((hasTouchScreen && isMobileWidth) || hasCoarsePointer) {
          setIsMobile(true);
          return;
        }

        // 3. 兜底用 userAgent（针对一些特殊设备）
        const ua = navigator.userAgent;
        if (/Android|iPhone|iPad|iPod|Mobile|Touch/i.test(ua)) {
          setIsMobile(true);
          return;
        }
      }
      setIsMobile(false);
    };

    checkMobile();

    // 监听屏幕尺寸变化（如设备旋转）
    const mediaQuery = window.matchMedia('(max-width: 1024px)');
    const handleMediaChange = () => checkMobile();

    // 使用现代API，兼容性已经很好了
    mediaQuery.addEventListener('change', handleMediaChange);

    return () => {
      mediaQuery.removeEventListener('change', handleMediaChange);
    };
  }, []);

  // 清理动画帧和重置状态
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  // 当抽屉关闭时重置滑动状态
  useEffect(() => {
    if (!isOpen) {
      setIsSwipeActive(false);
      setSwipeOffset(0);
      setShowSwipeHint(false);
      touchStateRef.current = null;
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      if (hintTimeoutRef.current) {
        clearTimeout(hintTimeoutRef.current);
        hintTimeoutRef.current = null;
      }
    } else if (isOpen && isMobile) {
      // 抽屉打开时，延迟显示滑动提示
      hintTimeoutRef.current = setTimeout(() => {
        setShowSwipeHint(true);
        // 3秒后自动隐藏提示
        hintTimeoutRef.current = setTimeout(() => {
          setShowSwipeHint(false);
        }, 3000);
      }, 1000);
    }
  }, [isOpen, isMobile]);

  // 增强的手势状态管理
  const touchStateRef = useRef<{
    startX: number;
    startY: number;
    startTime: number;
    lastX: number;
    lastY: number;
    isTracking: boolean;
    hasMovedSignificantly: boolean;
    direction: 'horizontal' | 'vertical' | null;
    velocity: number;
  } | null>(null);

  // 手势配置常量
  const GESTURE_CONFIG = {
    MIN_SWIPE_DISTANCE: 60,        // 最小滑动距离
    MIN_SWIPE_VELOCITY: 0.3,       // 最小滑动速度 (px/ms)
    DIRECTION_THRESHOLD: 10,       // 方向判断阈值
    DIRECTION_RATIO: 1.8,          // 水平/垂直比例阈值
    EDGE_ZONE_WIDTH: 80,           // 边缘检测区域宽度（增加到80px）
    MAX_VERTICAL_DEVIATION: 120,   // 最大垂直偏移
    SCROLL_AREA_MARGIN: 60,        // 滚动区域边距
  };

  // 检查是否在可滑动的边缘区域
  const isInSwipeZone = (x: number, y: number, element: HTMLElement) => {
    const rect = element.getBoundingClientRect();
    const isInLeftEdge = x <= GESTURE_CONFIG.EDGE_ZONE_WIDTH;
    const isInValidHeight = y >= rect.top + GESTURE_CONFIG.SCROLL_AREA_MARGIN &&
                           y <= rect.bottom - GESTURE_CONFIG.SCROLL_AREA_MARGIN;
    return isInLeftEdge && isInValidHeight;
  };

  const onTouchStart = (e: React.TouchEvent) => {
    if (!isMobile || e.touches.length !== 1) return;

    const touch = e.touches[0];
    const startX = touch.clientX;
    const startY = touch.clientY;

    // 检查是否在有效的滑动区域
    if (!drawerRef.current || !isInSwipeZone(startX, startY, drawerRef.current)) {
      return;
    }

    touchStateRef.current = {
      startX,
      startY,
      startTime: Date.now(),
      lastX: startX,
      lastY: startY,
      isTracking: true,
      hasMovedSignificantly: false,
      direction: null,
      velocity: 0,
    };

    // 开始滑动动画
    setIsSwipeActive(true);
    setSwipeOffset(0);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    if (!isMobile || !touchStateRef.current?.isTracking || e.touches.length !== 1) return;

    const touch = e.touches[0];
    const currentX = touch.clientX;
    const currentY = touch.clientY;
    const state = touchStateRef.current;

    const deltaX = currentX - state.startX;
    const deltaY = currentY - state.startY;
    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);

    // 确定滑动方向（只在第一次显著移动时确定）
    if (!state.hasMovedSignificantly && (absDeltaX > GESTURE_CONFIG.DIRECTION_THRESHOLD || absDeltaY > GESTURE_CONFIG.DIRECTION_THRESHOLD)) {
      state.hasMovedSignificantly = true;

      // 判断是否为水平滑动
      if (absDeltaX > absDeltaY * GESTURE_CONFIG.DIRECTION_RATIO) {
        state.direction = 'horizontal';
      } else if (absDeltaY > absDeltaX * GESTURE_CONFIG.DIRECTION_RATIO) {
        state.direction = 'vertical';
      }
    }

    // 只处理水平右滑手势
    if (state.direction === 'horizontal' && deltaX > 0) {
      // 计算滑动速度
      const timeDelta = Date.now() - state.startTime;
      state.velocity = timeDelta > 0 ? deltaX / timeDelta : 0;

      // 阻止默认滚动行为
      e.preventDefault();

      // 更新视觉反馈 - 使用阻尼效果
      const dampedOffset = Math.min(deltaX * 0.8, window.innerWidth * 0.7);

      // 使用 requestAnimationFrame 优化性能
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      animationFrameRef.current = requestAnimationFrame(() => {
        setSwipeOffset(dampedOffset);
      });

      // 更新位置
      state.lastX = currentX;
      state.lastY = currentY;
    } else if (state.direction === 'vertical') {
      // 如果是垂直滑动，停止跟踪并重置动画
      state.isTracking = false;
      setIsSwipeActive(false);
      setSwipeOffset(0);
    }
  };

  const onTouchEnd = (e: React.TouchEvent) => {
    if (!isMobile || !touchStateRef.current?.isTracking || e.changedTouches.length !== 1) return;

    const state = touchStateRef.current;
    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - state.startX;
    const deltaY = touch.clientY - state.startY;
    const timeDelta = Date.now() - state.startTime;

    // 计算最终速度
    const finalVelocity = timeDelta > 0 ? deltaX / timeDelta : 0;

    // 判断是否应该关闭抽屉
    const shouldClose = state.direction === 'horizontal' &&
                       deltaX > 0 && // 右滑
                       Math.abs(deltaY) < GESTURE_CONFIG.MAX_VERTICAL_DEVIATION && // 垂直偏移不能太大
                       (deltaX > GESTURE_CONFIG.MIN_SWIPE_DISTANCE || // 距离足够
                        finalVelocity > GESTURE_CONFIG.MIN_SWIPE_VELOCITY); // 或速度足够快

    // 清理动画帧
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    if (shouldClose) {
      // 添加关闭动画
      setSwipeOffset(window.innerWidth);
      setTimeout(() => {
        onClose();
        // 重置动画状态
        setIsSwipeActive(false);
        setSwipeOffset(0);
      }, 200);
    } else {
      // 回弹动画
      setSwipeOffset(0);
      setTimeout(() => {
        setIsSwipeActive(false);
      }, 200);
    }

    // 重置状态
    touchStateRef.current = null;
  };
  
  // 阻止浏览器历史导航
  useEffect(() => {
    const preventHistoryNavigation = () => {
      if (isOpen) {
        window.history.pushState(null, '', window.location.href);
      }
    };
    
    window.addEventListener('popstate', preventHistoryNavigation);
    
    if (isOpen) {
      window.history.pushState(null, '', window.location.href);
    }
    
    return () => {
      window.removeEventListener('popstate', preventHistoryNavigation);
    };
  }, [isOpen]);

  return (
    <>
      <div
        className={cn(
          "fixed inset-0 bg-black/20 dark:bg-white/10 transition-opacity duration-500 z-[60]",
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        )}
        onClick={onClose}
      />
      <div
        ref={drawerRef}
        className={cn(
          "fixed top-0 right-0 h-full overflow-y-auto w-full lg:w-[80%] bg-light-primary dark:bg-dark-primary shadow-[-8px_0_30px_rgba(0,0,0,0.1)] z-[70]",
          "ease-out transition-opacity duration-300 md:transform md:transition-transform md:duration-500",
          // 滑动时禁用默认过渡，使用自定义transform
          isSwipeActive ? "transition-none" : "",
          isOpen
            ? "opacity-100 md:translate-x-0"
            : "opacity-0 pointer-events-none md:opacity-100 md:translate-x-[105%]"
        )}
        style={{
          // 滑动时应用实时偏移
          transform: isSwipeActive && isMobile
            ? `translateX(${swipeOffset}px)`
            : undefined,
          // 滑动时调整阴影透明度
          boxShadow: isSwipeActive && isMobile
            ? `rgba(0, 0, 0, ${Math.max(0.1 - swipeOffset / window.innerWidth * 0.1, 0)}) -8px 0px 30px`
            : undefined,
        }}
        // 仅移动端绑定触控事件
        {...(isMobile
          ? {
              onTouchStart,
              onTouchMove,
              onTouchEnd,
            }
          : {})}
      >
        <div
          className={cn(
            "sticky z-40 top-0 left-0 right-0 flex items-center justify-between w-full py-2 px-4 text-sm",
            "shadow-xl dark:shadow-lg",
            "bg-gradient-to-r from-[#e3f0ff] to-[#f8fafc] dark:from-[#23272e] dark:to-[#181c20] backdrop-blur-md",
            "border-b border-light-200 dark:border-dark-200 transition-all duration-300"
          )}
        >
          {/* 左侧关闭按钮 */}
          <button
            onClick={onClose}
            className="flex items-center justify-center rounded-lg p-2 text-black dark:text-white hover:text-[#24A0ED] dark:hover:text-[#24A0ED] transition-colors duration-150"
            aria-label="关闭"
          >
            <X className="h-5 w-5" />
          </button>

          {/* 中间标题 */}
          {selectedChat?.chatId && (
            <div className="flex-1 flex flex-col items-center justify-center mx-2">
              <p className="truncate max-w-[200px] text-center text-base font-semibold">{selectedChat?.title}</p>
            </div>
          )}

          {/* 右侧操作按钮 */}
          <div className="w-[40px] flex justify-center">
            {selectedChat?.chatId && (
              <DeleteChat
                redirect={false}
                chatId={selectedChat.chatId}
                chats={[]}
                setChats={() => {}}
                onClose={onClose}
              />
            )}
          </div>
        </div>
        <div className="flex-1 flex flex-col px-4 relative">
          {/* 滑动提示 - 仅在移动端显示 */}
          {isMobile && showSwipeHint && (
            <div className={cn(
              "absolute left-0 top-1/2 -translate-y-1/2 z-50 pointer-events-none",
              "bg-black/70 dark:bg-white/70 text-white dark:text-black",
              "px-3 py-2 rounded-r-lg text-sm font-medium",
              "animate-pulse transition-opacity duration-500",
              "flex items-center space-x-2"
            )}>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span>从左边缘右滑关闭</span>
            </div>
          )}

          <ChatWindow
            id={selectedChat?.chatId}
            initialQuery={selectedChat?.chatId ? undefined : selectedChat?.url ? `Summary: ${selectedChat.url}` : undefined}
            discoverId={selectedChat?.discoverId}
            title={selectedChat?.title}
            onChatIdCreated={onChatIdCreated}
            showCloseButton={true}
            autoScroll={false}
          />
        </div>
      </div>
    </>
  );
};
