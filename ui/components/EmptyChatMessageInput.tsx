import { ArrowR<PERSON>, Sparkles } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import TextareaAutosize from 'react-textarea-autosize';
import CopilotToggle from './MessageInputActions/Copilot';
import Focus from './MessageInputActions/Focus';
import Optimization from './MessageInputActions/Optimization';
import Attach from './MessageInputActions/Attach';
import { File } from './ChatWindow';
import { cn } from '@/lib/utils';

const EmptyChatMessageInput = ({
  sendMessage,
  focusMode,
  setFocusMode,
  optimizationMode,
  setOptimizationMode,
  fileIds,
  setFileIds,
  files,
  setFiles,
}: {
  sendMessage: (message: string) => void;
  focusMode: string;
  setFocusMode: (mode: string) => void;
  optimizationMode: string;
  setOptimizationMode: (mode: string) => void;
  fileIds: string[];
  setFileIds: (fileIds: string[]) => void;
  files: File[];
  setFiles: (files: File[]) => void;
}) => {
  const [copilotEnabled, setCopilotEnabled] = useState(false);
  const [message, setMessage] = useState('');
  const [isInputActive, setIsInputActive] = useState(false);
  const [isComposing, setIsComposing] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement | null>(null);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const activeElement = document.activeElement;

      const isInputFocused =
        activeElement?.tagName === 'INPUT' ||
        activeElement?.tagName === 'TEXTAREA' ||
        activeElement?.hasAttribute('contenteditable');

      if (e.key === '/' && !isInputFocused) {
        e.preventDefault();
        setIsInputActive(true);
        inputRef.current?.focus();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        if (message.trim()) {
          sendMessage(message);
          setMessage('');
        }
      }}
      onKeyDown={(e) => {
        if (e.key === 'Enter' && !e.shiftKey && !isComposing && message.trim()) {
          e.preventDefault();
          sendMessage(message);
          setMessage('');
        }
      }}
      onCompositionStart={() => setIsComposing(true)}
      onCompositionEnd={() => setIsComposing(false)}
      className="w-full max-w-2xl"
    >
      <div className={cn(
        "flex flex-col px-5 pt-5 pb-3 rounded-xl w-full border shadow-md transition-all duration-300",
        "bg-white dark:bg-dark-secondary",
        "border-light-200 dark:border-dark-200",
        isInputActive 
          ? "ring-2 ring-[#24A0ED]/40 shadow-lg shadow-[#24A0ED]/10" 
          : "hover:border-[#24A0ED]/30 hover:shadow-lg"
      )}>
        <div className="relative">
          <TextareaAutosize
            ref={inputRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            minRows={2}
            onClick={() => setIsInputActive(true)}
            onFocus={() => setIsInputActive(true)}
            onBlur={() => setIsInputActive(false)}
            className={cn(
              "bg-transparent text-sm resize-none focus:outline-none w-full",
              "text-black dark:text-white",
              "placeholder:text-black/40 dark:placeholder:text-white/50",
              "max-h-24 lg:max-h-36 xl:max-h-48",
              "transition-all duration-200",
              "pr-6"
            )}
            placeholder="点击输入您的问题..."
          />
          {message.length === 0 && !isInputActive && (
            <div className="absolute right-0 top-0 text-black/30 dark:text-white/30 text-xs bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded">
              /
            </div>
          )}
        </div>
        
        <div className="flex flex-row items-center justify-between mt-4 pt-2 border-t border-gray-100 dark:border-gray-800">
          <div className="flex flex-row items-center space-x-2 lg:space-x-4">
            <Focus focusMode={focusMode} setFocusMode={setFocusMode} />
            <Attach
              fileIds={fileIds}
              setFileIds={setFileIds}
              files={files}
              setFiles={setFiles}
              showText
            />
          </div>
          <div className="flex flex-row items-center space-x-1 sm:space-x-4">
            <Optimization
              optimizationMode={optimizationMode}
              setOptimizationMode={setOptimizationMode}
            />
            <button
              type="submit"
              disabled={message.trim().length === 0}
              className={cn(
                "text-white rounded-full p-2.5 transition-all duration-300",
                "disabled:cursor-not-allowed",
                message.trim().length === 0 
                  ? "bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500" 
                  : "bg-[#24A0ED] hover:bg-[#1a8cd8] active:scale-95 shadow-md shadow-[#24A0ED]/20"
              )}
            >
              <ArrowRight size={18} className={message.trim().length === 0 ? "" : "text-white"} />
            </button>
          </div>
        </div>
      </div>
    </form>
  );
};

export default EmptyChatMessageInput;
