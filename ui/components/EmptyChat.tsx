import { Message<PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, Lightbulb } from 'lucide-react';
import EmptyChatMessageInput from './EmptyChatMessageInput';
import { useState } from 'react';
import Link from 'next/link';
import { File } from './ChatWindow';
import { cn } from '@/lib/utils';

const EmptyChat = ({
  sendMessage,
  focusMode,
  setFocusMode,
  optimizationMode,
  setOptimizationMode,
  fileIds,
  setFileIds,
  files,
  setFiles,
}: {
  sendMessage: (message: string) => void;
  focusMode: string;
  setFocusMode: (mode: string) => void;
  optimizationMode: string;
  setOptimizationMode: (mode: string) => void;
  fileIds: string[];
  setFileIds: (fileIds: string[]) => void;
  files: File[];
  setFiles: (files: File[]) => void;
}) => {
  return (
    <div className="relative w-full scrollbar-hide bg-transparent">
      <div className="flex flex-col items-center  max-w-screen-sm mx-auto px-2 py-6 sm:py-10 space-y-6 sm:space-y-8 pb-20">
        {/* 顶部图标区 */}
        <div className="flex flex-col items-center space-y-3 mt-4 sm:mt-8">
          <div
            className="relative flex items-center justify-center w-20 h-20 rounded-full shadow-lg glassmorphism transition-all"
            style={{
              background: 'linear-gradient(135deg, rgba(99,102,241,0.85) 0%, rgba(96,165,250,0.85) 100%)',
              boxShadow: '0 6px 32px 0 rgba(99,102,241,0.18), 0 1.5px 6px 0 rgba(96,165,250,0.10)',
              backdropFilter: 'blur(8px)',
              WebkitBackdropFilter: 'blur(8px)',
            }}
          >
            <MessageSquare className="w-10 h-10 text-white drop-shadow" />
            {/* 柔和高光 */}
            <span className="absolute left-2 top-2 w-6 h-6 rounded-full bg-white/30 blur-[10px] pointer-events-none" />
          </div>
          <div className="flex flex-col items-center space-y-1">
            <h2 className="text-neutral-900 dark:text-neutral-100 text-[2rem] sm:text-3xl font-bold tracking-tight text-center leading-tight font-sans">
              欢迎使用{process.env.NEXT_PUBLIC_SITE_TITLE}
            </h2>
            <p className="text-gray-500 dark:text-gray-400 text-base sm:text-lg text-center max-w-xs sm:max-w-md font-sans">
              开始一段新的对话，探索AI的无限可能
            </p>
          </div>
        </div>
        {/* 主内容卡片区 */}
        <div className="w-full max-w-lg flex flex-col items-center bg-white/80 dark:bg-neutral-900/80 rounded-2xl shadow-xl border border-gray-100 dark:border-neutral-800 px-3 sm:px-6 py-4 sm:py-6 space-y-4 transition-all">
          <EmptyChatMessageInput
            sendMessage={sendMessage}
            focusMode={focusMode}
            setFocusMode={setFocusMode}
            optimizationMode={optimizationMode}
            setOptimizationMode={setOptimizationMode}
            fileIds={fileIds}
            setFileIds={setFileIds}
            files={files}
            setFiles={setFiles}
          />
          {/* 历史消息记录按钮 */}
          <div className="w-full flex justify-center">
            <Link
              href="/library"
              className="inline-flex items-center px-5 py-2.5 rounded-full font-semibold text-white shadow-lg transition-all duration-200 bg-gradient-to-r from-indigo-500 via-blue-500 to-sky-400 hover:scale-105 hover:shadow-2xl focus:outline-none focus:ring-2 focus:ring-indigo-300 dark:focus:ring-indigo-700"
              style={{
                background: 'linear-gradient(90deg, #6366f1 0%, #60a5fa 100%)',
                boxShadow: '0 4px 16px 0 rgba(99,102,241,0.18), 0 1.5px 6px 0 rgba(96,165,250,0.10)',
              }}
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M8 6h13M8 12h13M8 18h13M3 6h.01M3 12h.01M3 18h.01" />
              </svg>
              历史消息记录
            </Link>
          </div>
        </div>
      </div>
      <style jsx global>{`
        /* 隐藏滚动条但保持可滚动功能 */
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .scrollbar-hide {
          scrollbar-width: none;
          -ms-overflow-style: none;
        }
        /* glassmorphism 效果 */
        .glassmorphism {
          background: linear-gradient(135deg, rgba(99,102,241,0.85) 0%, rgba(96,165,250,0.85) 100%);
          box-shadow: 0 6px 32px 0 rgba(99,102,241,0.18), 0 1.5px 6px 0 rgba(96,165,250,0.10);
          backdrop-filter: blur(8px);
          -webkit-backdrop-filter: blur(8px);
        }
        @media (max-width: 640px) {
          .glassmorphism {
            width: 64px !important;
            height: 64px !important;
          }
        }
      `}</style>
    </div>
  );
};

export default EmptyChat;
