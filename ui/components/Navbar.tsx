import { Clock, Edit, Share, Trash } from 'lucide-react';
import { Message } from './ChatWindow';
import { useEffect, useState } from 'react';
import { formatTimeDifference, withQuery } from '@/lib/utils';
import DeleteChat from './DeleteChat';

const Navbar = ({
  chatId,
  messages,
  showHomeButton = true,
}: {
  messages: Message[];
  chatId: string;
  showHomeButton?: boolean;
}) => {
  const [title, setTitle] = useState<string>('');
  const [timeAgo, setTimeAgo] = useState<string>('');

  useEffect(() => {
    if (messages.length > 0) {
      const newTitle =
        messages[0].content.length > 20
          ? `${messages[0].content.substring(0, 20).trim()}...`
          : messages[0].content;
      setTitle(newTitle);
      const newTimeAgo = formatTimeDifference(
        new Date(),
        messages[0].createdAt,
      );
      setTimeAgo(newTimeAgo);
    }
  }, [messages]);

  useEffect(() => {
    const intervalId = setInterval(() => {
      if (messages.length > 0) {
        const newTimeAgo = formatTimeDifference(
          new Date(),
          messages[0].createdAt,
        );
        setTimeAgo(newTimeAgo);
      }
    }, 1000);

    return () => clearInterval(intervalId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div
      className="
        sticky z-40 top-0 left-0 right-0
        flex flex-row items-center justify-between
        w-full max-w-3xl mx-auto
        py-3 px-3 sm:px-6 lg:px-8
        rounded-t-2xl shadow-xl dark:shadow-lg
        border border-light-200 dark:border-dark-200
        bg-gradient-to-r from-[#e3f0ff] to-[#f8fafc] dark:from-[#23272e] dark:to-[#181c20]
        backdrop-blur-md
        transition-all duration-300
        text-black dark:text-white/80
      "
    >
      {/* 左侧：主页/返回按钮 */}
      <div className="w-[40px] flex items-center">
        {showHomeButton && (
          <a
            href={withQuery('/')}
            className="
              flex items-center justify-center
              rounded-lg p-1
              text-black dark:text-white/80
              hover:text-[#24A0ED] dark:hover:text-[#24A0ED]
              hover:bg-[#e3f0ff]/60 dark:hover:bg-[#23272e]/60
              transition-colors duration-150
              active:scale-95
              cursor-pointer
              lg:hidden
            "
            aria-label="主页"
          >
            <Edit size={17} />
          </a>
        )}
      </div>

      {/* 中间：标题与时间 */}
      <div className="flex-1 flex flex-col items-center justify-center min-w-0">
        <div className="flex flex-row items-center justify-center gap-2">
          <Clock size={17} className="text-[#24A0ED] dark:text-[#24A0ED]" />
          <span className="text-xs text-black/60 dark:text-white/60">{timeAgo} ago</span>
        </div>
        <p className="truncate text-base sm:text-lg font-semibold mt-1 text-black dark:text-white">
          {title}
        </p>
      </div>

      {/* 右侧：操作按钮分组 */}
      <div className="w-[40px] flex justify-end items-center gap-1">
        <div
          className="
            flex items-center gap-1
            bg-white/60 dark:bg-[#23272e]/60
            rounded-lg px-1 py-0.5
            shadow-sm
          "
        >
          <DeleteChat
            redirect={true}
            chatId={chatId}
            chats={[]}
            setChats={() => {}}
          />
          {/* 可扩展更多操作按钮 */}
        </div>
      </div>
    </div>
  );
};

export default Navbar;
