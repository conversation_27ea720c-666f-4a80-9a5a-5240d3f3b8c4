'use client';

import { useEffect, useState } from 'react';

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
}

const Layout = ({ children, className }: LayoutProps) => {

  return (
    <main className={`bg-light-primary dark:bg-dark-primary h-screen flex flex-col ${className || ''}`}>
      <div
        className="w-full mx-0 flex-1 overflow-y-auto"
      >
        {children}
      </div>
    </main>
  );
};

export default Layout;
