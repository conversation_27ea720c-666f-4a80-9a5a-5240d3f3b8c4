{"name": "aiso-frontend", "version": "1.10.0-rc2", "license": "MIT", "author": "ItzCrazyKns", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format:write": "prettier . --write"}, "dependencies": {"@auth/core": "^0.37.4", "@headlessui/react": "^2.2.0", "@icons-pack/react-simple-icons": "^9.4.0", "@langchain/openai": "^0.0.25", "@tailwindcss/typography": "^0.5.12", "@types/react-syntax-highlighter": "^15.5.13", "clsx": "^2.1.0", "jose": "^5.10.0", "langchain": "^0.1.30", "lucide-react": "^0.363.0", "markdown-to-jsx": "^7.7.4", "next": "14.1.4", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "react": "^18", "react-dom": "^18", "react-syntax-highlighter": "^15.6.1", "react-text-to-speech": "^0.14.5", "react-textarea-autosize": "^8.5.3", "sonner": "^1.4.41", "tailwind-merge": "^2.2.2", "yet-another-react-lightbox": "^3.17.2", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.4", "postcss": "^8", "prettier": "^3.2.5", "tailwindcss": "^3.3.0", "typescript": "^5"}}