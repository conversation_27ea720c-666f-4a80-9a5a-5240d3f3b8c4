@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html, body {
    height: 100%;
    min-height: 100%;
  }

  /* iOS设备专用优化 */
  @supports (-webkit-touch-callout: none) {
    /* 优化iOS上的滚动行为 */
    .overflow-y-auto {
      -webkit-overflow-scrolling: touch;
      /* 确保滚动容器的层叠上下文正确 */
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
    }

    /* 防止iOS上的transform闪烁 */
    .transition-transform {
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
      -webkit-perspective: 1000px;
      perspective: 1000px;
      /* 确保transform的硬件加速正确 */
      -webkit-transform-style: preserve-3d;
      transform-style: preserve-3d;
    }

    /* 专门针对发现页面的滚动容器 */
    [data-topic] {
      /* 确保iOS上的滚动容器不会产生偏移 */
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
    }
  }

  .overflow-hidden-scrollable {
    -ms-overflow-style: none;
  }

  .overflow-hidden-scrollable::-webkit-scrollbar {
    display: none;
  }
}
