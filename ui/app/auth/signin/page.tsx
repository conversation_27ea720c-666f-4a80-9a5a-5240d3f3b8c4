'use client';

import { signIn } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function SignIn({
  searchParams,
}: {
  searchParams: { from?: string };
}) {
  const [error, setError] = useState(false);

  useEffect(() => {
    const handleSignIn = async () => {
      try {
        const result = await signIn('authelia', {
          callbackUrl: searchParams.from || '/'
        });

        if (result?.error) {
          setError(true);
          toast.error('登录失败，请检查您的凭据');
        } else if (result?.url) {
          window.location.href = result.url;
        }
      } catch (err) {
        setError(true);
        toast.error('登录过程中发生错误');
      }
    };

    handleSignIn();
  }, [searchParams.from]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-light-primary dark:bg-dark-primary">
      <div className="p-8 rounded-lg bg-light-secondary dark:bg-dark-secondary shadow-lg">
        <h1 className="text-2xl font-semibold mb-4 text-center dark:text-white">
          {error ? '登录失败' : '正在跳转到登录页面...'}
        </h1>
        {!error && (
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white"></div>
          </div>
        )}
        {error && (
          <p className="text-red-500 text-center">认证失败，请重试或联系管理员</p>
        )}
      </div>
    </div>
  );
}