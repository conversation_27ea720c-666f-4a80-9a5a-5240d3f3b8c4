'use client';

import { useState } from 'react';
import EmptyChat from '@/components/EmptyChat';
import type { File } from '@/components/ChatWindow';

import Layout from '@/components/Layout';
import { useRouter } from 'next/navigation';
import { ArrowLeft, BookOpenText } from 'lucide-react';
import { cn } from '@/lib/utils';

export default function AskPage() {
  const [focusMode, setFocusMode] = useState('default');
  const [optimizationMode, setOptimizationMode] = useState('default');
  const [fileIds, setFileIds] = useState<string[]>([]);
  const [files, setFiles] = useState<File[]>([]);

  const sendMessage = () => {};

  const router = useRouter();

  return (
    <Layout>
      <div className="sticky top-0 z-20 flex items-center gap-2 px-6 pt-5 pb-2 border-b border-light-200 dark:border-dark-200 bg-gradient-to-r from-[#e3f0ff] to-[#f8fafc] dark:from-[#23272e] dark:to-[#181c20]">
        <button
          type="button"
          onClick={() => router.push('/')}
          className="flex items-center space-x-1 text-sm text-black dark:text-white hover:text-[#24A0ED] dark:hover:text-[#24A0ED] font-normal cursor-pointer px-1 py-1 mr-1"
          aria-label="返回"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>返回</span>
        </button>
        <BookOpenText className="w-5 h-5 text-[#24A0ED] dark:text-[#24A0ED]" />
        <h1 className="text-xl sm:text-2xl font-semibold tracking-tight p-0">提问</h1>
      </div>
      <EmptyChat
        sendMessage={sendMessage}
        focusMode={focusMode}
        setFocusMode={setFocusMode}
        optimizationMode={optimizationMode}
        setOptimizationMode={setOptimizationMode}
        fileIds={fileIds}
        setFileIds={setFileIds}
        files={files}
        setFiles={setFiles}
      />
    </Layout>
  );
}
