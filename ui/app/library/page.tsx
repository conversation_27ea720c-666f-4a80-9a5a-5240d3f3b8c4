'use client';

import DeleteChat from '@/components/DeleteChat';
import { cn, formatTimeDifference, isAndroid } from '@/lib/utils';
import { BookOpenText, ClockIcon, ArrowLeft } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { ChatDrawer } from '@/components/discover/ChatDrawer';
import { toast } from 'sonner';
import { LoadingSpinner } from '@/components/LoadingSpinner';

export interface Chat {
  id: string;
  title: string;
  createdAt: string;
  focusMode: string;
}

const Page = () => {
  const router = useRouter();
  const [chats, setChats] = useState<Chat[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isPulling, setIsPulling] = useState(false);
  const threshold = 70;
  let lastY = 0;

  const fetchChats = async () => {
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/chats`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Device-ID': localStorage.getItem('deviceId')!,
        },
      });

      const data = await res.json();
      setChats(data.chats);
      return data.chats;
    } catch (err) {
      console.error('Error fetching chats:', err);
      toast.error('获取对话记录失败');
      return [];
    }
  };

  useEffect(() => {
    const init = async () => {
      setLoading(true);
      await fetchChats();
      setLoading(false);
    };

    init();
  }, []);

  const handlePullToRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchChats();
      toast.success('数据已更新', {
        duration: 1500,
      });
    } finally {
      setRefreshing(false);
    }
  }, []);

  const handleTouchStart = (e: React.TouchEvent) => {
    lastY = e.touches[0].clientY;
    setIsPulling(false);
    setPullDistance(0);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    const panel = e.currentTarget;
    const y = e.touches[0].clientY;
    const isAtTop = panel.scrollTop <= 0;
    
    if (isAtTop && !refreshing) {
      const distance = Math.max(0, Math.min(y - lastY, threshold));
      
      if (y > lastY) {
        setPullDistance(distance);
        setIsPulling(true);
        e.preventDefault();
      }
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (isPulling && pullDistance >= threshold) {
      handlePullToRefresh();
    }
    setPullDistance(0);
    setIsPulling(false);
  };

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedChat, setSelectedChat] = useState<{
    chatId?: string;
    discoverId?: string;
    url?: string;
    title?: string;
  } | null>(null);
  const [isAndroidDevice, setIsAndroidDevice] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsAndroidDevice(isAndroid());
    }
  }, []);

  return loading ? (
    <LoadingSpinner />
  ) : (
    <>
      <div className="h-screen min-h-0 flex flex-col bg-gradient-to-br from-[#f8fafc] via-[#e3e8ef] to-[#f1f5f9] dark:from-[#181c20] dark:via-[#23272e] dark:to-[#1a1d23] transition-colors duration-300">
        <div
          className={cn(
            "w-full h-full min-h-0 flex flex-col bg-white dark:bg-[#23272e] transition-all duration-300",
            isDrawerOpen ? "lg:mr-[65%]" : ""
          )}
        >
          <div className="sticky top-0 z-20 flex items-center gap-2 px-6 pt-5 pb-2 border-b border-light-200 dark:border-dark-200 bg-gradient-to-r from-[#e3f0ff] to-[#f8fafc] dark:from-[#23272e] dark:to-[#181c20]">
<button
  type="button"
  onClick={() => router.push('/ask')}
  className="flex items-center space-x-1 text-sm text-black dark:text-white hover:text-[#24A0ED] dark:hover:text-[#24A0ED] font-normal cursor-pointer px-1 py-1 mr-1"
  aria-label="返回"
>
  <ArrowLeft className="w-4 h-4" />
  <span>返回</span>
</button>
            <BookOpenText className="w-5 h-5 text-[#24A0ED] dark:text-[#24A0ED]" />
            <h1 className="text-xl sm:text-2xl font-semibold tracking-tight p-0">对话记录</h1>
          </div>

          <div
            className={cn(
              "flex-1 min-h-0 overflow-y-auto overscroll-none bg-transparent",
              isAndroidDevice ? 'pb-40' : 'pb-20', "lg:pb-2"
            )}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            <div
              className={cn(
                "absolute top-0 left-0 right-0 flex justify-center items-center h-10 -translate-y-full z-10",
                (isPulling || refreshing) ? "opacity-100" : "opacity-0",
                "transition-opacity duration-300"
              )}
            >
              {refreshing ? (
                <LoadingSpinner />
              ) : (
                <span className="text-xs text-black/70 dark:text-white/70">{pullDistance >= threshold ? '松开刷新' : '下拉刷新'}</span>
              )}
            </div>

            {chats.length === 0 && (
              <div className="flex flex-col items-center justify-center h-60 text-center">
                <p className="text-black/60 dark:text-white/60 text-base font-medium">
                  暂无对话记录
                </p>
                <p className="text-black/50 dark:text-white/50 text-xs mt-1">
                  开始新对话后将显示在此处
                </p>
              </div>
            )}

            {chats.length > 0 && (
              <div className="flex flex-col gap-y-2 px-2 sm:px-4">
                {chats.map((chat, i) => (
                  <div
                    className={cn(
                      "group flex flex-col gap-1 p-2 sm:p-3 rounded-lg bg-white dark:bg-[#23272e] transition-all duration-200 relative",
                      i !== chats.length - 1 ? "border-b border-light-200 dark:border-dark-200" : ""
                    )}
                    key={i}
                  >
                    <div className="flex items-center justify-between">
                      <button
                        onClick={() => {
                          setSelectedChat({
                            chatId: chat.id,
                            title: chat.title
                          });
                          setIsDrawerOpen(true);
                        }}
                        className="text-black dark:text-white text-[15px] font-medium truncate transition duration-200 hover:text-[#24A0ED] dark:hover:text-[#24A0ED] cursor-pointer text-left w-full"
                      >
                        {chat.title}
                      </button>
                      <DeleteChat
                        chatId={chat.id}
                        chats={chats}
                        setChats={setChats}
                      />
                    </div>
                    <div className="flex flex-row items-center gap-1 text-black/60 dark:text-white/60 text-xs">
                      <ClockIcon size={13} className="mr-1" />
                      <span>
                        {formatTimeDifference(new Date(), chat.createdAt)} 前
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {isDrawerOpen && (
          <ChatDrawer
            isOpen={isDrawerOpen}
            onClose={() => {
              setIsDrawerOpen(false);
              setSelectedChat(null);
            }}
            selectedChat={selectedChat}
            onChatIdCreated={() => {}}
          />
        )}
      </div>
    </>
  );
};

export default Page;
