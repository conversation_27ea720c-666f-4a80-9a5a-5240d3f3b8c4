import clsx, { ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export const cn = (...classes: ClassValue[]) => twMerge(clsx(...classes));

export const isMobile = (): Boolean => {
  return window.innerWidth < 768;
};

export const formatTimeDifference = (
  date1: Date | string,
  date2: Date | string,
): string => {
  date1 = new Date(date1);
  date2 = new Date(date2);

  const diffInSeconds = Math.floor(
    Math.abs(date2.getTime() - date1.getTime()) / 1000,
  );

  if (diffInSeconds < 60)
    return `${diffInSeconds} 秒`;
  else if (diffInSeconds < 3600)
    return `${Math.floor(diffInSeconds / 60)} 分钟`;
  else if (diffInSeconds < 86400)
    return `${Math.floor(diffInSeconds / 3600)} 小时`;
  else if (diffInSeconds < 31536000)
    return `${Math.floor(diffInSeconds / 86400)} 天`;
  else
    return `${Math.floor(diffInSeconds / 31536000)} 年`;
};

export function getQuery() {
  if(typeof window === 'undefined' || !window.location.search){
    return new URLSearchParams();
  }
  return new URLSearchParams(window.location.search);
}

export function withQuery(url: string, params?: Record<string, any>) {
  const query = getQuery();
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      query.set(key, String(value));
    });
  }
  return url + '?' + query.toString();
}

export function loadImage(src: string) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    // 先设置事件监听器
    img.onload = () => resolve(src);
    img.onerror = (error) => {
      console.error('图片加载失败:', src, error);
      reject(new Error(`Failed to load image: ${src}`));
    };

    // 设置跨域属性
    img.crossOrigin = 'anonymous';
    img.referrerPolicy = 'no-referrer';
    img.decoding = 'async';
    img.loading = 'lazy';

    // 最后设置 src 触发加载
    img.src = src;

    // 添加超时处理
    const timeout = setTimeout(() => {
      img.src = '';
      reject(new Error(`Image load timeout: ${src}`));
    }, 10000); // 10秒超时

    img.onload = () => {
      clearTimeout(timeout);
      resolve(img);
    };

    img.onerror = (error) => {
      clearTimeout(timeout);
      console.error('图片加载失败:', src, error);
      reject(new Error(`Failed to load image: ${src}`));
    };
  });
}

/**
 * 检测当前设备是否为 Android
 * @returns {boolean} 如果是 Android 设备则返回 true，否则返回 false
 */
export const isAndroid = (): boolean => {
  if (typeof window === 'undefined') {
    return false;
  }
  const userAgent = window.navigator.userAgent.toLowerCase();
  console.log(userAgent);
  return userAgent.indexOf('android') > -1;
};

/**
 * 检测当前设备是否为 iOS
 * @returns {boolean} 如果是 iOS 设备则返回 true，否则返回 false
 */
export const isIOS = (): boolean => {
  if (typeof window === 'undefined') {
    return false;
  }
  const userAgent = window.navigator.userAgent.toLowerCase();
  return /iphone|ipad|ipod/.test(userAgent);
};