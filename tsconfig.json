{
  "compilerOptions": {
    "lib": ["ESNext", "DOM"],
    "module": "Node16",
    "moduleResolution": "Node16",
    "target": "ESNext",
    "outDir": "dist",
    "sourceMap": false,
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "allowSyntheticDefaultImports": true,
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "baseUrl": ".",
    "paths": {}
  },
  "include": [
    "src/**/*",
  ],
  "exclude": ["node_modules", "**/*.spec.ts", "src/scrape/**/*"]
}
