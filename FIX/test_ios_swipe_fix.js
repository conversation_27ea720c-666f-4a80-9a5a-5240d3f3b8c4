/**
 * iOS滑动切换主题修复测试脚本
 * 在浏览器控制台中运行此脚本来测试修复效果
 */

// 检测是否为iOS设备
function isIOSDevice() {
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

// 检查transform状态
function checkTransformState() {
  const topicElements = document.querySelectorAll('[data-topic]');
  const transformElements = document.querySelectorAll('.transition-transform');
  
  console.log('=== iOS滑动修复状态检查 ===');
  console.log('是否为iOS设备:', isIOSDevice());
  console.log('主题容器数量:', topicElements.length);
  console.log('Transform元素数量:', transformElements.length);
  
  topicElements.forEach((element, index) => {
    const computedStyle = window.getComputedStyle(element);
    const transform = computedStyle.transform;
    const scrollTop = element.scrollTop;
    
    console.log(`主题容器 ${index + 1}:`, {
      id: element.getAttribute('data-topic'),
      transform: transform,
      scrollTop: scrollTop,
      offsetHeight: element.offsetHeight,
      scrollHeight: element.scrollHeight
    });
  });
  
  transformElements.forEach((element, index) => {
    const computedStyle = window.getComputedStyle(element);
    const transform = computedStyle.transform;
    const transition = computedStyle.transition;
    
    console.log(`Transform元素 ${index + 1}:`, {
      transform: transform,
      transition: transition,
      backfaceVisibility: computedStyle.backfaceVisibility,
      transformStyle: computedStyle.transformStyle
    });
  });
}

// 模拟滑动测试
function simulateSwipeTest() {
  console.log('=== 开始模拟滑动测试 ===');
  
  const topicElement = document.querySelector('[data-topic]');
  if (!topicElement) {
    console.error('未找到主题容器元素');
    return;
  }
  
  const transformElement = topicElement.querySelector('.transition-transform');
  if (!transformElement) {
    console.error('未找到transform元素');
    return;
  }
  
  // 模拟滑动开始
  console.log('1. 模拟滑动开始...');
  transformElement.style.transform = 'translateX(100px)';
  
  setTimeout(() => {
    console.log('2. 检查滑动中状态...');
    checkTransformState();
    
    // 模拟滑动结束，重置状态
    console.log('3. 模拟滑动结束，重置状态...');
    transformElement.style.transition = 'none';
    transformElement.style.transform = 'translateY(0px) translateX(0px)';
    
    // 强制重排
    transformElement.offsetHeight;
    
    // 恢复过渡
    setTimeout(() => {
      transformElement.style.transition = '';
      console.log('4. 状态重置完成，检查最终状态...');
      checkTransformState();
    }, 16);
    
  }, 1000);
}

// 监听主题切换事件
function monitorTopicSwitch() {
  console.log('=== 开始监听主题切换 ===');
  
  // 监听localStorage变化（主题切换会更新localStorage）
  const originalSetItem = localStorage.setItem;
  localStorage.setItem = function(key, value) {
    if (key === 'selectedDiscoverTab') {
      console.log('检测到主题切换:', value);
      setTimeout(() => {
        console.log('主题切换后状态检查:');
        checkTransformState();
      }, 100);
    }
    return originalSetItem.apply(this, arguments);
  };
  
  console.log('主题切换监听已启用');
}

// 检查CSS支持
function checkCSSSupport() {
  console.log('=== CSS支持检查 ===');
  
  const testElement = document.createElement('div');
  document.body.appendChild(testElement);
  
  // 检查@supports规则
  const supportsWebkitTouchCallout = CSS.supports('-webkit-touch-callout', 'none');
  console.log('支持-webkit-touch-callout:', supportsWebkitTouchCallout);
  
  // 检查transform3d支持
  testElement.style.transform = 'translate3d(0, 0, 0)';
  const supportsTransform3d = testElement.style.transform !== '';
  console.log('支持transform3d:', supportsTransform3d);
  
  // 检查backface-visibility支持
  testElement.style.backfaceVisibility = 'hidden';
  const supportsBackfaceVisibility = testElement.style.backfaceVisibility !== '';
  console.log('支持backface-visibility:', supportsBackfaceVisibility);
  
  document.body.removeChild(testElement);
}

// 主测试函数
function runIOSSwipeFixTest() {
  console.clear();
  console.log('🔧 iOS滑动切换主题修复测试');
  console.log('================================');
  
  checkCSSSupport();
  checkTransformState();
  monitorTopicSwitch();
  
  console.log('\n可用的测试命令:');
  console.log('- checkTransformState(): 检查当前transform状态');
  console.log('- simulateSwipeTest(): 模拟滑动测试');
  console.log('- monitorTopicSwitch(): 监听主题切换');
  
  if (isIOSDevice()) {
    console.log('\n✅ 当前为iOS设备，修复应该生效');
  } else {
    console.log('\n⚠️ 当前非iOS设备，修复不会应用');
  }
}

// 导出测试函数到全局作用域
window.iosSwipeFixTest = {
  run: runIOSSwipeFixTest,
  checkTransformState,
  simulateSwipeTest,
  monitorTopicSwitch,
  checkCSSSupport,
  isIOSDevice
};

// 自动运行测试
if (typeof window !== 'undefined') {
  console.log('iOS滑动修复测试脚本已加载');
  console.log('运行 iosSwipeFixTest.run() 开始测试');
}
